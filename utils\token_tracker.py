"""
Token使用追踪器 - 监控和管理LLM API的token使用情况
用于成本控制、性能分析和使用统计
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import json
import logging
from pathlib import Path
import threading
from collections import defaultdict, deque


class TokenType(Enum):
    """Token类型枚举"""
    INPUT = "input"  # 输入token
    OUTPUT = "output"  # 输出token
    TOTAL = "total"  # 总token
    CACHED = "cached"  # 缓存token
    REASONING = "reasoning"  # 推理token（如DeepSeek Reasoner）


class OperationType(Enum):
    """操作类型枚举"""
    PLANNING = "planning"  # 规划操作
    WRITING = "writing"  # 写作操作
    EXAMINING = "examining"  # 审查操作
    REVISING = "revising"  # 修订操作
    SEARCHING = "searching"  # 搜索操作
    STRUCTURING = "structuring"  # 结构化操作
    QUALITY_CHECK = "quality_check"  # 质量检查
    CONTENT_GENERATION = "content_generation"  # 内容生成
    RRAG_RETRIEVAL = "rrag_retrieval"  # RRAG检索
    SIMILARITY_ANALYSIS = "similarity_analysis"  # 相似性分析


@dataclass
class TokenUsageRecord:
    """Token使用记录"""
    timestamp: datetime
    operation: OperationType
    agent_name: str
    token_type: TokenType
    count: int

    # 详细信息
    model_name: str = "unknown"
    cost_estimate: float = 0.0
    response_time: float = 0.0  # 响应时间（秒）

    # 上下文信息
    session_id: str = ""
    workflow_id: str = ""
    node_id: str = ""

    # 内容相关
    input_length: int = 0  # 输入文本长度
    output_length: int = 0  # 输出文本长度
    complexity_score: float = 1.0  # 复杂度评分

    # 质量指标
    success: bool = True
    error_message: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = asdict(self)
        # 转换枚举为字符串
        result['operation'] = self.operation.value
        result['token_type'] = self.token_type.value
        result['timestamp'] = self.timestamp.isoformat()
        return result


class CostCalculator:
    """成本计算器"""

    # 主流模型的定价（每1000个token的成本，美元）
    MODEL_PRICING = {
        "gpt-4": {"input": 0.03, "output": 0.06},
        "gpt-4-turbo": {"input": 0.01, "output": 0.03},
        "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
        "deepseek-chat": {"input": 0.00014, "output": 0.00028},
        "deepseek-reasoner": {"input": 0.00055, "output": 0.00219},
        "claude-3-opus": {"input": 0.015, "output": 0.075},
        "claude-3-sonnet": {"input": 0.003, "output": 0.015},
        "claude-3-haiku": {"input": 0.00025, "output": 0.00125},
    }

    @classmethod
    def calculate_cost(cls,
                       model_name: str,
                       input_tokens: int,
                       output_tokens: int,
                       reasoning_tokens: int = 0) -> Dict[str, float]:
        """
        计算使用成本

        Args:
            model_name: 模型名称
            input_tokens: 输入token数
            output_tokens: 输出token数
            reasoning_tokens: 推理token数（针对推理模型）

        Returns:
            成本详细信息
        """
        # 规范化模型名称
        normalized_name = cls._normalize_model_name(model_name)

        if normalized_name not in cls.MODEL_PRICING:
            # 未知模型，使用默认定价
            pricing = {"input": 0.001, "output": 0.002}
        else:
            pricing = cls.MODEL_PRICING[normalized_name]

        # 计算基础成本
        input_cost = (input_tokens / 1000) * pricing["input"]
        output_cost = (output_tokens / 1000) * pricing["output"]

        # 推理模型的额外成本
        reasoning_cost = 0.0
        if reasoning_tokens > 0 and "reasoner" in normalized_name:
            # 推理token通常成本更高
            reasoning_cost = (reasoning_tokens / 1000) * pricing["output"] * 1.5

        total_cost = input_cost + output_cost + reasoning_cost

        return {
            "input_cost": round(input_cost, 6),
            "output_cost": round(output_cost, 6),
            "reasoning_cost": round(reasoning_cost, 6),
            "total_cost": round(total_cost, 6),
            "currency": "USD"
        }

    @classmethod
    def _normalize_model_name(cls, model_name: str) -> str:
        """规范化模型名称"""
        model_name = model_name.lower()

        # 映射规则
        if "gpt-4" in model_name:
            if "turbo" in model_name:
                return "gpt-4-turbo"
            return "gpt-4"
        elif "gpt-3.5" in model_name:
            return "gpt-3.5-turbo"
        elif "deepseek" in model_name:
            if "reasoner" in model_name:
                return "deepseek-reasoner"
            return "deepseek-chat"
        elif "claude-3" in model_name:
            if "opus" in model_name:
                return "claude-3-opus"
            elif "sonnet" in model_name:
                return "claude-3-sonnet"
            elif "haiku" in model_name:
                return "claude-3-haiku"

        return model_name


class TokenTracker:
    """Token使用追踪器 - 增强版本"""

    def __init__(self,
                 session_id: str = None,
                 auto_save: bool = True,
                 save_interval: int = 300,  # 5分钟自动保存
                 max_records: int = 10000):
        """
        初始化Token追踪器

        Args:
            session_id: 会话ID
            auto_save: 是否自动保存
            save_interval: 自动保存间隔（秒）
            max_records: 最大记录数量
        """
        self.session_id = session_id or f"session_{int(datetime.now().timestamp())}"
        self.auto_save = auto_save
        self.save_interval = save_interval
        self.max_records = max_records

        # 记录存储
        self.usage_records: deque = deque(maxlen=max_records)
        self.current_session_stats = self._init_session_stats()

        # 实时统计缓存
        self._stats_cache = {}
        self._cache_valid = False

        # 线程安全
        self._lock = threading.RLock()

        # 日志记录器
        self.logger = logging.getLogger("AutoPatent.TokenTracker")

        # 自动保存定时器
        self._save_timer = None
        if auto_save:
            self._start_auto_save()

        self.logger.info(f"Token追踪器已初始化，session_id: {self.session_id}")

    def _init_session_stats(self) -> Dict[str, Any]:
        """初始化session统计"""
        return {
            "session_id": self.session_id,
            "start_time": datetime.now(),
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_reasoning_tokens": 0,
            "total_cost": 0.0,
            "api_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "operations": defaultdict(int),
            "models_used": defaultdict(int),
            "agents_used": defaultdict(int)
        }

    def add_tokens(self,
                   operation: Union[OperationType, str],
                   agent_name: str,
                   input_tokens: int = 0,
                   output_tokens: int = 0,
                   reasoning_tokens: int = 0,
                   model_name: str = "unknown",
                   response_time: float = 0.0,
                   workflow_id: str = "",
                   node_id: str = "",
                   success: bool = True,
                   error_message: str = "",
                   input_length: int = 0,
                   output_length: int = 0,
                   complexity_score: float = 1.0) -> str:
        """
        添加token使用记录

        Args:
            operation: 操作类型
            agent_name: 智能体名称
            input_tokens: 输入token数
            output_tokens: 输出token数
            reasoning_tokens: 推理token数
            model_name: 模型名称
            response_time: 响应时间
            workflow_id: 工作流ID
            node_id: 节点ID
            success: 是否成功
            error_message: 错误信息
            input_length: 输入文本长度
            output_length: 输出文本长度
            complexity_score: 复杂度评分

        Returns:
            记录ID
        """
        with self._lock:
            # 转换操作类型
            if isinstance(operation, str):
                try:
                    operation = OperationType(operation)
                except ValueError:
                    operation = OperationType.CONTENT_GENERATION

            timestamp = datetime.now()

            # 计算成本
            cost_info = CostCalculator.calculate_cost(
                model_name, input_tokens, output_tokens, reasoning_tokens
            )

            # 创建记录
            if input_tokens > 0:
                input_record = TokenUsageRecord(
                    timestamp=timestamp,
                    operation=operation,
                    agent_name=agent_name,
                    token_type=TokenType.INPUT,
                    count=input_tokens,
                    model_name=model_name,
                    cost_estimate=cost_info["input_cost"],
                    response_time=response_time,
                    session_id=self.session_id,
                    workflow_id=workflow_id,
                    node_id=node_id,
                    input_length=input_length,
                    output_length=output_length,
                    complexity_score=complexity_score,
                    success=success,
                    error_message=error_message
                )
                self.usage_records.append(input_record)

            if output_tokens > 0:
                output_record = TokenUsageRecord(
                    timestamp=timestamp,
                    operation=operation,
                    agent_name=agent_name,
                    token_type=TokenType.OUTPUT,
                    count=output_tokens,
                    model_name=model_name,
                    cost_estimate=cost_info["output_cost"],
                    response_time=response_time,
                    session_id=self.session_id,
                    workflow_id=workflow_id,
                    node_id=node_id,
                    input_length=input_length,
                    output_length=output_length,
                    complexity_score=complexity_score,
                    success=success,
                    error_message=error_message
                )
                self.usage_records.append(output_record)

            if reasoning_tokens > 0:
                reasoning_record = TokenUsageRecord(
                    timestamp=timestamp,
                    operation=operation,
                    agent_name=agent_name,
                    token_type=TokenType.REASONING,
                    count=reasoning_tokens,
                    model_name=model_name,
                    cost_estimate=cost_info["reasoning_cost"],
                    response_time=response_time,
                    session_id=self.session_id,
                    workflow_id=workflow_id,
                    node_id=node_id,
                    input_length=input_length,
                    output_length=output_length,
                    complexity_score=complexity_score,
                    success=success,
                    error_message=error_message
                )
                self.usage_records.append(reasoning_record)

            # 更新session统计
            self.current_session_stats["total_input_tokens"] += input_tokens
            self.current_session_stats["total_output_tokens"] += output_tokens
            self.current_session_stats["total_reasoning_tokens"] += reasoning_tokens
            self.current_session_stats["total_cost"] += cost_info["total_cost"]
            self.current_session_stats["api_calls"] += 1

            if success:
                self.current_session_stats["successful_calls"] += 1
            else:
                self.current_session_stats["failed_calls"] += 1

            self.current_session_stats["operations"][operation.value] += 1
            self.current_session_stats["models_used"][model_name] += 1
            self.current_session_stats["agents_used"][agent_name] += 1

            # 使缓存失效
            self._cache_valid = False

            record_id = f"{timestamp.isoformat()}_{agent_name}_{operation.value}"

            self.logger.debug(f"添加token使用记录: {record_id}")

            return record_id

    def get_usage(self) -> Dict[str, Any]:
        """获取当前session的token使用统计"""
        with self._lock:
            stats = self.current_session_stats.copy()

            # 计算session持续时间
            duration = datetime.now() - stats["start_time"]
            stats["duration_seconds"] = duration.total_seconds()
            stats["duration_formatted"] = str(duration).split('.')[0]  # 去掉微秒

            # 计算总token数
            stats["total_tokens"] = (stats["total_input_tokens"] +
                                     stats["total_output_tokens"] +
                                     stats["total_reasoning_tokens"])

            # 计算平均响应时间
            if self.usage_records:
                total_response_time = sum(r.response_time for r in self.usage_records)
                stats["average_response_time"] = total_response_time / len(self.usage_records)
            else:
                stats["average_response_time"] = 0.0

            # 计算成功率
            if stats["api_calls"] > 0:
                stats["success_rate"] = stats["successful_calls"] / stats["api_calls"] * 100
            else:
                stats["success_rate"] = 0.0

            # 转换默认字典为普通字典
            stats["operations"] = dict(stats["operations"])
            stats["models_used"] = dict(stats["models_used"])
            stats["agents_used"] = dict(stats["agents_used"])

            return stats

    def get_detailed_usage(self,
                           time_range: Optional[timedelta] = None,
                           operation_filter: Optional[List[OperationType]] = None,
                           agent_filter: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        获取详细的使用分析

        Args:
            time_range: 时间范围过滤
            operation_filter: 操作类型过滤
            agent_filter: 智能体过滤

        Returns:
            详细分析结果
        """
        with self._lock:
            # 过滤记录
            filtered_records = list(self.usage_records)

            if time_range:
                cutoff_time = datetime.now() - time_range
                filtered_records = [r for r in filtered_records if r.timestamp >= cutoff_time]

            if operation_filter:
                filtered_records = [r for r in filtered_records if r.operation in operation_filter]

            if agent_filter:
                filtered_records = [r for r in filtered_records if r.agent_name in agent_filter]

            if not filtered_records:
                return {"message": "没有符合条件的记录"}

            # 统计分析
            analysis = {
                "总记录数": len(filtered_records),
                "时间范围": {
                    "开始时间": min(r.timestamp for r in filtered_records).isoformat(),
                    "结束时间": max(r.timestamp for r in filtered_records).isoformat()
                }
            }

            # 按token类型统计
            token_stats = defaultdict(int)
            cost_stats = defaultdict(float)

            for record in filtered_records:
                token_stats[record.token_type.value] += record.count
                cost_stats[record.token_type.value] += record.cost_estimate

            analysis["token统计"] = dict(token_stats)
            analysis["成本统计"] = {k: round(v, 6) for k, v in cost_stats.items()}
            analysis["总成本"] = round(sum(cost_stats.values()), 6)

            # 按操作类型统计
            operation_stats = defaultdict(lambda: {"次数": 0, "tokens": 0, "成本": 0.0})

            for record in filtered_records:
                op = record.operation.value
                operation_stats[op]["次数"] += 1
                operation_stats[op]["tokens"] += record.count
                operation_stats[op]["成本"] += record.cost_estimate

            analysis["操作统计"] = {
                op: {
                    "次数": stats["次数"],
                    "tokens": stats["tokens"],
                    "成本": round(stats["成本"], 6)
                }
                for op, stats in operation_stats.items()
            }

            # 按智能体统计
            agent_stats = defaultdict(lambda: {"次数": 0, "tokens": 0, "成本": 0.0})

            for record in filtered_records:
                agent = record.agent_name
                agent_stats[agent]["次数"] += 1
                agent_stats[agent]["tokens"] += record.count
                agent_stats[agent]["成本"] += record.cost_estimate

            analysis["智能体统计"] = {
                agent: {
                    "次数": stats["次数"],
                    "tokens": stats["tokens"],
                    "成本": round(stats["成本"], 6)
                }
                for agent, stats in agent_stats.items()
            }

            # 按模型统计
            model_stats = defaultdict(lambda: {"次数": 0, "tokens": 0, "成本": 0.0})

            for record in filtered_records:
                model = record.model_name
                model_stats[model]["次数"] += 1
                model_stats[model]["tokens"] += record.count
                model_stats[model]["成本"] += record.cost_estimate

            analysis["模型统计"] = {
                model: {
                    "次数": stats["次数"],
                    "tokens": stats["tokens"],
                    "成本": round(stats["成本"], 6)
                }
                for model, stats in model_stats.items()
            }

            # 性能统计
            successful_records = [r for r in filtered_records if r.success]
            failed_records = [r for r in filtered_records if not r.success]

            analysis["性能统计"] = {
                "成功率": len(successful_records) / len(filtered_records) * 100 if filtered_records else 0,
                "平均响应时间": sum(r.response_time for r in filtered_records) / len(
                    filtered_records) if filtered_records else 0,
                "最快响应": min(r.response_time for r in filtered_records) if filtered_records else 0,
                "最慢响应": max(r.response_time for r in filtered_records) if filtered_records else 0,
                "失败次数": len(failed_records)
            }

            # 时间趋势分析（按小时）
            hourly_stats = defaultdict(lambda: {"tokens": 0, "成本": 0.0, "调用次数": 0})

            for record in filtered_records:
                hour_key = record.timestamp.strftime("%Y-%m-%d %H:00")
                hourly_stats[hour_key]["tokens"] += record.count
                hourly_stats[hour_key]["成本"] += record.cost_estimate
                hourly_stats[hour_key]["调用次数"] += 1

            analysis["时间趋势"] = dict(hourly_stats)

            return analysis

    def estimate_cost(self,
                      input_rate: float = None,
                      output_rate: float = None,
                      model_name: str = "deepseek-chat") -> Dict[str, float]:
        """
        估算token使用成本

        Args:
            input_rate: 输入token费率（每1000token）
            output_rate: 输出token费率（每1000token）
            model_name: 模型名称（用于获取默认费率）

        Returns:
            成本估算信息
        """
        usage = self.get_usage()

        # 如果没有提供费率，使用模型默认费率
        if input_rate is None or output_rate is None:
            pricing = CostCalculator.MODEL_PRICING.get(
                CostCalculator._normalize_model_name(model_name),
                {"input": 0.001, "output": 0.002}
            )
            input_rate = input_rate or pricing["input"]
            output_rate = output_rate or pricing["output"]

        input_cost = (usage['total_input_tokens'] / 1000) * input_rate
        output_cost = (usage['total_output_tokens'] / 1000) * output_rate
        reasoning_cost = (usage['total_reasoning_tokens'] / 1000) * output_rate * 1.5  # 推理token成本更高
        total_cost = input_cost + output_cost + reasoning_cost

        return {
            "input_cost": round(input_cost, 6),
            "output_cost": round(output_cost, 6),
            "reasoning_cost": round(reasoning_cost, 6),
            "total_cost": round(total_cost, 6),
            "input_rate": input_rate,
            "output_rate": output_rate,
            "currency": "USD"
        }

    def get_efficiency_metrics(self) -> Dict[str, Any]:
        """获取效率指标"""
        with self._lock:
            if not self.usage_records:
                return {"message": "没有使用记录"}

            # 计算各种效率指标
            total_tokens = sum(record.count for record in self.usage_records)
            total_time = sum(record.response_time for record in self.usage_records)
            total_cost = sum(record.cost_estimate for record in self.usage_records)

            # 字符到token的转换效率
            total_chars = sum(record.input_length + record.output_length for record in self.usage_records)
            char_to_token_ratio = total_chars / total_tokens if total_tokens > 0 else 0

            # 按复杂度分析
            complexity_groups = defaultdict(list)
            for record in self.usage_records:
                if record.complexity_score <= 1.0:
                    complexity_groups["简单"].append(record)
                elif record.complexity_score <= 2.0:
                    complexity_groups["中等"].append(record)
                else:
                    complexity_groups["复杂"].append(record)

            complexity_analysis = {}
            for level, records in complexity_groups.items():
                if records:
                    avg_tokens = sum(r.count for r in records) / len(records)
                    avg_time = sum(r.response_time for r in records) / len(records)
                    avg_cost = sum(r.cost_estimate for r in records) / len(records)

                    complexity_analysis[level] = {
                        "记录数": len(records),
                        "平均tokens": round(avg_tokens, 2),
                        "平均响应时间": round(avg_time, 2),
                        "平均成本": round(avg_cost, 6)
                    }

            return {
                "总体效率": {
                    "token处理速度": round(total_tokens / total_time, 2) if total_time > 0 else 0,
                    "成本效率": round(total_tokens / total_cost, 2) if total_cost > 0 else 0,
                    "字符token转换比": round(char_to_token_ratio, 2),
                    "平均单次调用tokens": round(total_tokens / len(self.usage_records), 2)
                },
                "复杂度分析": complexity_analysis,
                "操作效率排名": self._get_operation_efficiency_ranking()
            }

    def _get_operation_efficiency_ranking(self) -> List[Dict[str, Any]]:
        """获取操作效率排名"""
        operation_metrics = defaultdict(lambda: {
            "总tokens": 0, "总时间": 0, "总成本": 0, "调用次数": 0
        })

        for record in self.usage_records:
            op = record.operation.value
            operation_metrics[op]["总tokens"] += record.count
            operation_metrics[op]["总时间"] += record.response_time
            operation_metrics[op]["总成本"] += record.cost_estimate
            operation_metrics[op]["调用次数"] += 1

        # 计算效率指标并排名
        efficiency_list = []
        for operation, metrics in operation_metrics.items():
            if metrics["调用次数"] > 0:
                tokens_per_second = (metrics["总tokens"] / metrics["总时间"]) if metrics["总时间"] > 0 else 0
                cost_per_token = metrics["总成本"] / metrics["总tokens"] if metrics["总tokens"] > 0 else 0

                efficiency_list.append({
                    "操作": operation,
                    "调用次数": metrics["调用次数"],
                    "token处理速度": round(tokens_per_second, 2),
                    "单token成本": round(cost_per_token, 8),
                    "平均响应时间": round(metrics["总时间"] / metrics["调用次数"], 2)
                })

        # 按token处理速度排序
        efficiency_list.sort(key=lambda x: x["token处理速度"], reverse=True)
        return efficiency_list

    def reset_session(self, new_session_id: str = None):
        """重置session统计"""
        with self._lock:
            if new_session_id:
                self.session_id = new_session_id
            else:
                self.session_id = f"session_{int(datetime.now().timestamp())}"

            self.current_session_stats = self._init_session_stats()
            self._cache_valid = False

            self.logger.info(f"Session已重置，新session_id: {self.session_id}")

    def export_usage(self,
                     filepath: Optional[Union[str, Path]] = None,
                     format_type: str = "json",
                     include_raw_data: bool = True) -> str:
        """
        导出使用记录

        Args:
            filepath: 文件路径
            format_type: 格式类型 (json, csv, xlsx)
            include_raw_data: 是否包含原始数据

        Returns:
            导出的文件路径
        """
        with self._lock:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if filepath is None:
                filename = f"token_usage_{self.session_id}_{timestamp}.{format_type}"
                filepath = Path(filename)
            else:
                filepath = Path(filepath)
                # 检查filepath是否为目录，如果是，则在其中创建带时间戳的文件
                if filepath.is_dir() or not filepath.suffix:
                    filename = f"token_usage_{self.session_id}_{timestamp}.{format_type}"
                    filepath = filepath / filename

            # 确保父目录存在
            filepath.parent.mkdir(parents=True, exist_ok=True)

            # 准备导出数据
            export_data = {
                "导出信息": {
                    "导出时间": datetime.now().isoformat(),
                    "session_id": self.session_id,
                    "记录数量": len(self.usage_records),
                    "格式版本": "1.0"
                },
                "session统计": self.get_usage(),
                "详细分析": self.get_detailed_usage(),
                "成本估算": self.estimate_cost()
            }

            if include_raw_data:
                export_data["原始记录"] = [record.to_dict() for record in self.usage_records]

            # 根据格式导出
            if format_type.lower() == "json":
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)

            elif format_type.lower() == "csv":
                try:
                    import pandas as pd
                    # 将记录转换为DataFrame
                    records_df = pd.DataFrame([record.to_dict() for record in self.usage_records])
                    records_df.to_csv(filepath, index=False, encoding='utf-8-sig')
                except ImportError:
                    self.logger.warning("pandas未安装，无法导出CSV格式")
                    raise ImportError("需要安装pandas来支持CSV导出")

            elif format_type.lower() == "xlsx":
                try:
                    import pandas as pd

                    with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                        # session统计
                        session_df = pd.DataFrame([export_data["session统计"]])
                        session_df.to_excel(writer, sheet_name='Session统计', index=False)

                        # 原始记录
                        if include_raw_data:
                            records_df = pd.DataFrame([record.to_dict() for record in self.usage_records])
                            records_df.to_excel(writer, sheet_name='使用记录', index=False)

                        # 成本分析
                        cost_df = pd.DataFrame([export_data["成本估算"]])
                        cost_df.to_excel(writer, sheet_name='成本分析', index=False)

                except ImportError:
                    self.logger.warning("pandas或openpyxl未安装，无法导出Excel格式")
                    raise ImportError("需要安装pandas和openpyxl来支持Excel导出")

            else:
                raise ValueError(f"不支持的导出格式: {format_type}")

            self.logger.info(f"使用记录已导出到: {filepath}")
            return str(filepath)

    def _start_auto_save(self):
        """启动自动保存"""

        def auto_save():
            try:
                self.export_usage(format_type="json")
                self.logger.debug("自动保存完成")
            except Exception as e:
                self.logger.error(f"自动保存失败: {e}")
            finally:
                # 重新设置定时器
                if self.auto_save:
                    self._save_timer = threading.Timer(self.save_interval, auto_save)
                    self._save_timer.daemon = True
                    self._save_timer.start()

        self._save_timer = threading.Timer(self.save_interval, auto_save)
        self._save_timer.daemon = True
        self._save_timer.start()

    def stop_auto_save(self):
        """停止自动保存"""
        self.auto_save = False
        if self._save_timer:
            self._save_timer.cancel()
            self._save_timer = None

    def __del__(self):
        """析构函数 - 确保停止自动保存"""
        self.stop_auto_save()

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if self.auto_save and self.usage_records:
            # 保存最终记录
            try:
                # 使用固定路径进行自动保存，避免保存到当前工作目录
                self.export_usage(filepath='../output/token_records', format_type="json")
            except Exception as e:
                self.logger.error(f"最终保存失败: {e}")
        self.stop_auto_save()


if __name__ == "__main__":
    # 基本使用
    with TokenTracker(session_id="patent_gen_001") as tracker:
        # 记录token使用
        tracker.add_tokens(
            operation=OperationType.PLANNING,
            agent_name="PlannerAgent",
            input_tokens=1500,
            output_tokens=800,
            model_name="deepseek-chat",
            response_time=2.5,
            success=True
        )

        # 获取使用统计
        usage_stats = tracker.get_usage()
        print(f"总token数: {usage_stats['total_tokens']:,}")
        print(f"总成本: ${usage_stats['total_cost']:.4f}")

        # 导出报告
        report_path = tracker.export_usage(filepath='../output/token_records', format_type="JSON", include_raw_data=False)