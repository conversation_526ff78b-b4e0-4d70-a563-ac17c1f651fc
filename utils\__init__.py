"""
工具模块 - Utilities and Helper Functions

提供Token追踪、可视化、PGTree处理、LLM客户端等
核心工具和辅助功能。
"""

from .token_tracker import TokenTracker, OperationType
from .token_visualizer import TokenUsageVisualizer

# from .pg_tree_handler import (
#     PG<PERSON><PERSON>Hand<PERSON>,
#     PG<PERSON>reeNode,
#     NodeStatus,
#     NodeType,
#     ExecutionMode as PGTreeExecutionMode,
#     NodeMetrics
# )
#
# from .llm_client import (
#     LLMClient,
#     LLMClientManager,
#     ModelProvider,
#     ModelConfig,
#     Message,
#     LLMResponse,
#     ResponseFormat
# )
#
# # 便捷函数
# def setup_token_tracking(session_name: str = None,
#                         enable_visualization: bool = True) -> tuple:
#     """
#     设置Token追踪和可视化
#
#     Args:
#         session_name: 会话名称
#         enable_visualization: 是否启用可视化
#
#     Returns:
#         (TokenTracker, TokenVisualizer) 元组
#     """
#     tracker = TokenTracker(session_name=session_name)
#
#     if enable_visualization:
#         visualizer = TokenUsageVisualizer(tracker)
#         return tracker, visualizer
#     else:
#         return tracker, None
#
# def create_llm_client(provider: ModelProvider,
#                      model_name: str,
#                      api_key: str,
#                      base_url: str = "",
#                      **kwargs) -> LLMClient:
#     """
#     创建LLM客户端
#
#     Args:
#         provider: 模型提供商
#         model_name: 模型名称
#         api_key: API密钥
#         base_url: API基础URL
#         **kwargs: 其他配置参数
#
#     Returns:
#         配置好的LLM客户端
#     """
#     config = ModelConfig(
#         provider=provider,
#         model_name=model_name,
#         api_key=api_key,
#         base_url=base_url,
#         **kwargs
#     )
#
#     return LLMClient(config)
#
# def setup_llm_clients(configs: dict,
#                      token_tracker: TokenTracker = None) -> LLMClientManager:
#     """
#     设置多个LLM客户端
#
#     Args:
#         configs: 客户端配置字典 {name: config_dict}
#         token_tracker: Token追踪器
#
#     Returns:
#         配置好的LLM客户端管理器
#     """
#     manager = LLMClientManager(token_tracker)
#
#     for name, config_dict in configs.items():
#         provider = ModelProvider(config_dict['provider'])
#         config = ModelConfig(
#             provider=provider,
#             model_name=config_dict['model_name'],
#             api_key=config_dict['api_key'],
#             base_url=config_dict.get('base_url', ''),
#             **{k: v for k, v in config_dict.items()
#                if k not in ['provider', 'model_name', 'api_key', 'base_url']}
#         )
#
#         is_default = config_dict.get('is_default', False)
#         manager.add_client(name, config, set_as_default=is_default)
#
#     return manager
#
# def create_pgtree_handler(tree_id: str = None,
#                          execution_mode: PGTreeExecutionMode = PGTreeExecutionMode.SEQUENTIAL,
#                          max_parallel_nodes: int = 3) -> PGTreeHandler:
#     """
#     创建PGTree处理器
#
#     Args:
#         tree_id: 树ID
#         execution_mode: 执行模式
#         max_parallel_nodes: 最大并行节点数
#
#     Returns:
#         配置好的PGTree处理器
#     """
#     return PGTreeHandler(
#         tree_id=tree_id,
#         execution_mode=execution_mode,
#         max_parallel_nodes=max_parallel_nodes
#     )
#
# # 配置验证函数
# def validate_system_setup() -> dict:
#     """
#     验证系统设置
#
#     Returns:
#         验证结果字典
#     """
#     result = {
#         'valid': True,
#         'issues': [],
#         'warnings': []
#     }
#
#     # 检查必要依赖
#     try:
#         import openai
#         import aiohttp
#         import httpx
#         import sqlite3
#         import pandas
#         import matplotlib
#         import plotly
#     except ImportError as e:
#         result['valid'] = False
#         result['issues'].append(f"缺少必要依赖: {e}")
#
#     # 检查Python版本
#     import sys
#     if sys.version_info < (3, 8):
#         result['valid'] = False
#         result['issues'].append(f"Python版本过低: {sys.version}")
#
#     # 检查内存
#     try:
#         import psutil
#         available_memory = psutil.virtual_memory().available / (1024**3)  # GB
#         if available_memory < 2:
#             result['warnings'].append(f"可用内存较低: {available_memory:.1f}GB")
#     except ImportError:
#         result['warnings'].append("无法检查内存状态（psutil未安装）")
#
#     return result
#
# # 系统信息函数
# def get_system_info() -> dict:
#     """获取系统信息"""
#     import sys
#     import platform
#
#     info = {
#         'python_version': sys.version,
#         'platform': platform.platform(),
#         'processor': platform.processor(),
#         'architecture': platform.architecture(),
#     }
#
#     try:
#         import psutil
#         info.update({
#             'cpu_count': psutil.cpu_count(),
#             'memory_total': f"{psutil.virtual_memory().total / (1024**3):.1f}GB",
#             'memory_available': f"{psutil.virtual_memory().available / (1024**3):.1f}GB"
#         })
#     except ImportError:
#         info['system_stats'] = "psutil未安装，无法获取详细系统信息"
#
#     return info
#
# # 导出的公共接口
# __all__ = [
#     # Token相关
#     "TokenTracker",
#     "OperationType",
#     "TokenUsageEntry",
#     "SessionSummary",
#     "TokenVisualizer",
#     "ChartType",
#     "VisualizationConfig",
#
#     # PGTree相关
#     "PGTreeHandler",
#     "PGTreeNode",
#     "NodeStatus",
#     "NodeType",
#     "PGTreeExecutionMode",
#     "NodeMetrics",
#
#     # LLM客户端相关
#     "LLMClient",
#     "LLMClientManager",
#     "ModelProvider",
#     "ModelConfig",
#     "Message",
#     "LLMResponse",
#     "ResponseFormat",
#
#     # 便捷函数
#     "setup_token_tracking",
#     "create_llm_client",
#     "setup_llm_clients",
#     "create_pgtree_handler",
#
#     # 系统函数
#     "validate_system_setup",
#     "get_system_info",
# ]