"""
写作智能体 - 负责生成专利文档的各个section内容
"""

import logging
from typing import Dict, Any, List, Optional, Union
from enum import Enum
from datetime import datetime
import re
import json

from .base_agent import BaseAgent
from ..utils.pg_tree_handler import PG<PERSON><PERSON><PERSON><PERSON><PERSON>, NodeStatus
from ..utils.token_tracker import OperationType


class WritingStyle(Enum):
    """写作风格枚举"""
    FORMAL = "formal"  # 正式学术风格
    TECHNICAL = "technical"  # 技术专业风格
    LEGAL = "legal"  # 法律规范风格
    DESCRIPTIVE = "descriptive"  # 描述性风格


class SectionType(Enum):
    """专利section类型枚举"""
    ABSTRACT = "abstract"  # 摘要
    BACKGROUND = "background"  # 背景技术
    SUMMARY = "summary"  # 发明内容
    DETAILED_DESCRIPTION = "detailed_description"  # 具体实施方式
    CLAIMS = "claims"  # 权利要求书
    DRAWINGS = "drawings"  # 附图说明
    TECHNICAL_FIELD = "technical_field"  # 技术领域


class ContentTemplate:
    """内容模板类"""

    ABSTRACT_TEMPLATE = """
    {technical_field}的{invention_name}，其特征在于：{main_features}。

    该发明解决了{technical_problems}的问题，通过{solution_approach}实现。

    有益效果：{beneficial_effects}。
    """

    BACKGROUND_TEMPLATE = """
    ## 技术领域

    本发明涉及{technical_field}，特别是涉及{specific_area}。

    ## 背景技术

    {prior_art_description}

    现有技术存在以下问题：
    {existing_problems}

    ## 发明内容

    针对现有技术的不足，本发明的目的是提供{invention_purpose}。
    """

    CLAIMS_TEMPLATE = """
    权利要求书

    1. {independent_claim_1}

    2. 根据权利要求1所述的{invention_name}，其特征在于：{dependent_claim_1}。

    3. 根据权利要求1所述的{invention_name}，其特征在于：{dependent_claim_2}。
    """


class WriterAgent(BaseAgent):
    """写作智能体 - 专门负责专利内容生成"""

    def __init__(self,
                 writer_type: str = "general",
                 writing_style: WritingStyle = WritingStyle.FORMAL,
                 **kwargs):
        """
        初始化写作智能体

        Args:
            writer_type: 写作器类型 (general, technical, legal)
            writing_style: 写作风格
            **kwargs: 基类参数
        """
        super().__init__(agent_name=f"WriterAgent_{writer_type}", **kwargs)

        self.writer_type = writer_type
        self.writing_style = writing_style

        # 专业化配置
        self.specialization_config = {
            "general": {
                "focus_sections": ["abstract", "summary", "background"],
                "tone": "客观专业",
                "complexity_level": "中等"
            },
            "technical": {
                "focus_sections": ["detailed_description", "background", "technical_field"],
                "tone": "技术精确",
                "complexity_level": "高"
            },
            "legal": {
                "focus_sections": ["claims", "abstract"],
                "tone": "法律严谨",
                "complexity_level": "高"
            }
        }

        # 写作指南和模板
        self.writing_guidelines = self._load_writing_guidelines()
        self.content_templates = self._load_content_templates()

        # 质量控制参数
        self.quality_standards = {
            "min_word_count": {
                "abstract": 150,
                "background": 500,
                "summary": 400,
                "detailed_description": 800,
                "claims": 200
            },
            "max_word_count": {
                "abstract": 400,
                "background": 1500,
                "summary": 1000,
                "detailed_description": 3000,
                "claims": 1000
            },
            "required_elements": {
                "abstract": ["技术领域", "技术方案", "有益效果"],
                "background": ["技术领域", "现有技术", "存在问题"],
                "summary": ["发明目的", "技术方案", "有益效果"],
                "detailed_description": ["具体实施例", "技术细节"],
                "claims": ["独立权利要求", "从属权利要求"]
            }
        }

        # 写作统计
        self.writing_stats = {
            "total_sections_written": 0,
            "total_words_generated": 0,
            "average_writing_time": 0.0,
            "revisions_made": 0,
            "quality_scores": []
        }

        self.logger.info(f"WriterAgent初始化完成 - 类型: {writer_type}, 风格: {writing_style.value}")

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理写作请求的主方法

        Args:
            input_data: 包含pgtree、section、context等信息

        Returns:
            写作结果字典
        """
        start_time = datetime.now()

        try:
            # 解析输入数据
            pgtree = input_data.get('pgtree')
            section = input_data.get('section')
            context = input_data.get('context', {})
            workflow_id = input_data.get('workflow_id', '')

            if not pgtree or not section:
                return self._create_error_result("缺少必要的输入参数: pgtree或section")

            self.logger.info(f"开始写作section: {section}")

            # 检查是否适合当前writer类型
            if not self._is_section_suitable(section):
                self.logger.warning(f"Section {section} 不适合当前writer类型 {self.writer_type}")
                # 继续处理，但会影响质量

            # 获取section的写作要求
            section_requirements = self._get_section_requirements(pgtree, section)

            # 准备写作context
            writing_context = self._prepare_writing_context(pgtree, section, context)

            # 生成内容
            content_result = self._generate_section_content(section, section_requirements, writing_context)

            if not content_result.get('success'):
                return self._create_error_result(f"内容生成失败: {content_result.get('error')}")

            generated_content = content_result['content']

            # 质量检查和优化
            quality_result = self._perform_quality_check(section, generated_content)

            # 如果质量不达标，进行优化
            if quality_result['score'] < 6.0:
                self.logger.info(f"质量评分 {quality_result['score']:.2f} 偏低，开始优化...")
                optimized_result = self._optimize_content(section, generated_content, quality_result['issues'])

                if optimized_result.get('success'):
                    generated_content = optimized_result['content']
                    quality_result = self._perform_quality_check(section, generated_content)
                    self.writing_stats['revisions_made'] += 1

            # 更新PGTree
            updated_pgtree = self._update_pgtree(pgtree, section, generated_content, quality_result['score'])

            # 记录统计信息
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_writing_stats(section, generated_content, processing_time, quality_result['score'])

            # 记录token使用
            input_tokens, output_tokens = self._estimate_token_usage(writing_context, generated_content)
            self.token_tracker.add_tokens(
                operation=OperationType.WRITING,
                agent_name=self.agent_name,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                model_name="deepseek-chat",
                response_time=processing_time,
                workflow_id=workflow_id,
                node_id=section,
                success=True,
                input_length=len(str(writing_context)),
                output_length=len(generated_content)
            )

            self.logger.info(f"Section {section} 写作完成，质量评分: {quality_result['score']:.2f}")

            return {
                'success': True,
                'section': section,
                'content': generated_content,
                'pgtree': updated_pgtree,
                'quality_score': quality_result['score'],
                'quality_details': quality_result,
                'writing_metadata': {
                    'writer_type': self.writer_type,
                    'writing_style': self.writing_style.value,
                    'word_count': len(generated_content.split()),
                    'processing_time': processing_time,
                    'revision_made': self.writing_stats['revisions_made'] > 0
                }
            }

        except Exception as e:
            self.logger.error(f"写作处理失败: {e}")
            return self._create_error_result(f"写作异常: {e}")

    def revise_content(self,
                       current_content: str,
                       feedback: str,
                       section: str) -> str:
        """
        基于feedback修订现有内容

        Args:
            current_content: 当前内容
            feedback: 修订建议
            section: section名称

        Returns:
            修订后的内容
        """
        start_time = datetime.now()

        try:
            self.logger.info(f"开始修订section {section}")

            # 分析feedback
            revision_requirements = self._analyze_feedback(feedback)

            # 生成修订prompt
            revision_prompt = self._create_revision_prompt(current_content, feedback, section, revision_requirements)

            # 调用LLM进行修订
            revised_content = self._generate_response(
                prompt=revision_prompt,
                system_prompt=self._get_revision_system_prompt(section),
                temperature=0.7
            )

            # 验证修订结果
            if self._validate_revision(current_content, revised_content, revision_requirements):
                processing_time = (datetime.now() - start_time).total_seconds()

                # 记录统计
                self.writing_stats['revisions_made'] += 1

                self.logger.info(f"Section {section} 修订完成，耗时 {processing_time:.2f}秒")
                return revised_content
            else:
                self.logger.warning("修订结果验证失败，返回原内容")
                return current_content

        except Exception as e:
            self.logger.error(f"内容修订失败: {e}")
            return current_content

    def _is_section_suitable(self, section: str) -> bool:
        """检查section是否适合当前writer类型"""
        if self.writer_type not in self.specialization_config:
            return True  # general类型可以处理所有section

        focus_sections = self.specialization_config[self.writer_type]["focus_sections"]
        return section in focus_sections or self.writer_type == "general"

    def _get_section_requirements(self, pgtree: PGTreeHandler, section: str) -> Dict[str, Any]:
        """获取section的写作要求"""
        # 从PGTree获取section的具体要求
        if hasattr(pgtree, 'nodes') and section in pgtree.nodes:
            node_data = pgtree.nodes[section]
            return {
                'requirements': getattr(node_data, 'requirements', ''),
                'writing_guide': getattr(node_data, 'writing_guide', ''),
                'priority': getattr(node_data, 'priority', 'medium'),
                'estimated_complexity': getattr(node_data, 'estimated_complexity', 1.0),
                'dependencies': getattr(node_data, 'dependencies', [])
            }

        # 默认要求
        return {
            'requirements': f"生成高质量的{section}内容",
            'writing_guide': self.writing_guidelines.get(section, ''),
            'priority': 'medium',
            'estimated_complexity': 1.0,
            'dependencies': []
        }

    def _prepare_writing_context(self,
                                 pgtree: PGTreeHandler,
                                 section: str,
                                 context: Dict[str, Any]) -> Dict[str, Any]:
        """准备写作上下文信息"""
        writing_context = {
            'section': section,
            'writer_type': self.writer_type,
            'writing_style': self.writing_style.value,
            'concept': context.get('concept', ''),
            'technical_field': context.get('options', {}).get('technical_field', ''),
            'patent_type': context.get('options', {}).get('patent_type', 'invention'),
            'target_length': context.get('options', {}).get('target_length', 'standard'),
            'quality_requirements': self.quality_standards.get(section, {}),
            'related_content': {}
        }

        # 添加已完成section的内容作为参考
        if hasattr(pgtree, 'nodes'):
            for node_id, node_data in pgtree.nodes.items():
                if (hasattr(node_data, 'status') and
                        node_data.status == NodeStatus.COMPLETED and
                        hasattr(node_data, 'content') and
                        node_data.content):
                    writing_context['related_content'][node_id] = {
                        'content': node_data.content[:500],  # 截取前500字符
                        'section_name': getattr(node_data, 'section_name', node_id)
                    }

        # 添加section间的依赖信息
        section_requirements = self._get_section_requirements(pgtree, section)
        dependencies = section_requirements.get('dependencies', [])

        writing_context['dependency_content'] = {}
        for dep_section in dependencies:
            if dep_section in writing_context['related_content']:
                writing_context['dependency_content'][dep_section] = writing_context['related_content'][dep_section]

        return writing_context

    def _generate_section_content(self,
                                  section: str,
                                  requirements: Dict[str, Any],
                                  context: Dict[str, Any]) -> Dict[str, Any]:
        """生成section内容的核心方法"""
        try:
            # 构建写作prompt
            writing_prompt = self._build_writing_prompt(section, requirements, context)

            # 获取适当的system prompt
            system_prompt = self._get_system_prompt(section, context['writer_type'])

            # 调用LLM生成内容
            generated_content = self._generate_response(
                prompt=writing_prompt,
                system_prompt=system_prompt,
                temperature=self._get_temperature_for_section(section),
                max_tokens=self._get_max_tokens_for_section(section)
            )

            # 后处理内容
            processed_content = self._post_process_content(section, generated_content, context)

            return {
                'success': True,
                'content': processed_content,
                'raw_content': generated_content,
                'metadata': {
                    'section': section,
                    'word_count': len(processed_content.split()),
                    'generation_method': 'llm_direct'
                }
            }

        except Exception as e:
            self.logger.error(f"生成section内容失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _build_writing_prompt(self,
                              section: str,
                              requirements: Dict[str, Any],
                              context: Dict[str, Any]) -> str:
        """构建写作prompt"""
        # 基础prompt模板
        base_prompt = f"""
请为专利申请生成{section}部分的内容。

专利概念：
{context.get('concept', '')}

技术领域：{context.get('technical_field', '')}
专利类型：{context.get('patent_type', '')}
目标长度：{context.get('target_length', '')}

具体要求：
{requirements.get('requirements', '')}

写作指南：
{requirements.get('writing_guide', '')}
        """

        # 添加相关内容参考
        if context.get('dependency_content'):
            base_prompt += "\n\n相关已完成部分（供参考）：\n"
            for dep_section, dep_info in context['dependency_content'].items():
                base_prompt += f"\n{dep_info['section_name']}:\n{dep_info['content']}\n"

        # 添加section特定的要求
        section_specific_prompt = self._get_section_specific_prompt(section, context)
        if section_specific_prompt:
            base_prompt += f"\n\n{section_specific_prompt}"

        # 添加质量要求
        quality_reqs = context.get('quality_requirements', {})
        if quality_reqs:
            min_words = quality_reqs.get('min_word_count', 0)
            max_words = quality_reqs.get('max_word_count', 0)
            required_elements = quality_reqs.get('required_elements', [])

            base_prompt += f"\n\n质量要求："
            if min_words > 0:
                base_prompt += f"\n- 最少{min_words}字"
            if max_words > 0:
                base_prompt += f"\n- 最多{max_words}字"
            if required_elements:
                base_prompt += f"\n- 必须包含：{', '.join(required_elements)}"

        base_prompt += f"\n\n请生成专业、准确、符合专利申请规范的{section}内容："

        return base_prompt

    def _get_section_specific_prompt(self, section: str, context: Dict[str, Any]) -> str:
        """获取section特定的prompt补充"""
        section_prompts = {
            "abstract": """
摘要要求：
- 简明扼要地说明发明内容
- 包含技术领域、技术问题、解决方案、有益效果
- 语言精练，逻辑清晰
- 不超过400字
            """,
            "background": """
背景技术要求：
- 客观描述相关技术领域的现状
- 分析现有技术的不足和存在的问题
- 为本发明的必要性提供依据
- 避免主观评价，保持中性语调
            """,
            "summary": """
发明内容要求：
- 明确说明发明的目的和要解决的技术问题
- 详细描述技术方案的核心内容
- 突出发明的创新点和技术特征
- 说明发明的有益效果和应用价值
            """,
            "detailed_description": """
具体实施方式要求：
- 结合附图详细说明发明的实施例
- 描述技术方案的具体实现方法
- 提供足够的技术细节使本领域技术人员能够实施
- 可以包含多个实施例和变型
            """,
            "claims": """
权利要求书要求：
- 独立权利要求应完整地描述发明的技术方案
- 从属权利要求应进一步限定独立权利要求的技术特征
- 语言准确、逻辑严密、层次清晰
- 保护范围适当，既要有一定的宽度，又要避免过于宽泛
            """
        }

        return section_prompts.get(section, "")

    def _get_system_prompt(self, section: str, writer_type: str) -> str:
        """获取适合section和writer类型的system prompt"""
        base_system = """
你是一名专业的专利代理师，具有丰富的专利申请撰写经验。你的任务是生成高质量、符合法律规范的专利申请文档内容。

请遵循以下原则：
1. 内容准确、逻辑清晰、语言规范
2. 符合中国专利法律法规的要求
3. 保持客观、专业的写作风格
4. 注重技术细节的准确性
5. 确保内容的完整性和连贯性
        """

        # 根据writer类型调整
        type_specific = {
            "technical": """
你特别擅长技术描述，能够准确理解复杂的技术方案，并用专业的技术语言进行清晰的表达。
注重技术原理的阐述和实施细节的描述。
            """,
            "legal": """
你特别擅长法律条文的撰写，能够准确把握专利权利要求的撰写规范，确保保护范围的合理性。
注重用词的准确性和逻辑的严密性。
            """,
            "general": """
你具有综合的专利撰写能力，能够处理专利申请的各个部分，保持整体的协调性和一致性。
            """
        }

        section_specific = {
            "claims": """
对于权利要求书，你必须特别注意：
- 独立权利要求的完整性和清晰性
- 从属权利要求的逻辑层次
- 技术特征的准确表达
- 保护范围的合理设定
            """
        }

        system_prompt = base_system
        if writer_type in type_specific:
            system_prompt += type_specific[writer_type]
        if section in section_specific:
            system_prompt += section_specific[section]

        return system_prompt

    def _get_temperature_for_section(self, section: str) -> float:
        """获取适合section的temperature参数"""
        # 不同section需要不同的创造性水平
        section_temperatures = {
            "claims": 0.3,  # 权利要求需要严格准确
            "abstract": 0.5,  # 摘要需要简洁准确
            "background": 0.7,  # 背景可以有一定灵活性
            "summary": 0.6,  # 发明内容需要平衡
            "detailed_description": 0.8  # 具体实施方式可以更有创造性
        }

        return section_temperatures.get(section, 0.7)

    def _get_max_tokens_for_section(self, section: str) -> int:
        """获取适合section的max_tokens参数"""
        section_max_tokens = {
            "abstract": 600,
            "background": 1800,
            "summary": 1200,
            "detailed_description": 2500,
            "claims": 1000,
            "drawings": 400
        }

        return section_max_tokens.get(section, 1500)

    def _post_process_content(self, section: str, content: str, context: Dict[str, Any]) -> str:
        """后处理生成的内容"""
        processed_content = content.strip()

        # 移除可能的markdown格式
        processed_content = re.sub(r'^```.*?\n', '', processed_content, flags=re.MULTILINE)
        processed_content = re.sub(r'\n```$', '', processed_content)

        # section特定的后处理
        if section == "claims":
            processed_content = self._post_process_claims(processed_content)
        elif section == "abstract":
            processed_content = self._post_process_abstract(processed_content)

        # 统一格式化
        processed_content = self._format_content(processed_content, section)

        return processed_content

    def _post_process_claims(self, content: str) -> str:
        """后处理权利要求书内容"""
        lines = content.split('\n')
        processed_lines = []
        claim_number = 1

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 确保权利要求编号格式正确
            if re.match(r'^\d+\.', line):
                # 重新编号
                line = re.sub(r'^\d+\.', f'{claim_number}.', line)
                claim_number += 1
            elif line and not line.startswith('权利要求') and not line.startswith('Claims'):
                # 如果是权利要求内容但没有编号，添加编号
                if claim_number == 1 or not processed_lines:
                    line = f'{claim_number}. {line}'
                    claim_number += 1

            processed_lines.append(line)

        return '\n\n'.join(processed_lines)

    def _post_process_abstract(self, content: str) -> str:
        """后处理摘要内容"""
        # 确保摘要长度合适
        words = content.split()
        if len(words) > 300:
            # 截取到合适长度
            content = ' '.join(words[:300]) + '...'

        # 移除标题（如果存在）
        if content.startswith('摘要') or content.startswith('Abstract'):
            lines = content.split('\n')
            content = '\n'.join(lines[1:]).strip()

        return content

    def _format_content(self, content: str, section: str) -> str:
        """统一格式化内容"""
        # 标准化段落间距
        content = re.sub(r'\n{3,}', '\n\n', content)

        # 移除首尾空白
        content = content.strip()

        # section特定格式化
        if section in ["background", "summary", "detailed_description"]:
            # 确保主要部分有适当的结构
            if not re.search(r'##|第\d+部分|一、|1\.', content):
                # 如果没有明显的结构标记，保持原样
                pass

        return content

    def _perform_quality_check(self, section: str, content: str) -> Dict[str, Any]:
        """执行内容质量检查"""
        quality_score = 0.0
        issues = []
        details = {}

        # 基础检查
        word_count = len(content.split())
        details['word_count'] = word_count

        # 长度检查
        min_words = self.quality_standards['min_word_count'].get(section, 0)
        max_words = self.quality_standards['max_word_count'].get(section, float('inf'))

        if word_count < min_words:
            issues.append(f"内容过短，当前{word_count}字，最少需要{min_words}字")
            quality_score -= 1.0
        elif word_count > max_words:
            issues.append(f"内容过长，当前{word_count}字，最多允许{max_words}字")
            quality_score -= 0.5
        else:
            quality_score += 2.0

        # 必要元素检查
        required_elements = self.quality_standards['required_elements'].get(section, [])
        missing_elements = []

        for element in required_elements:
            if element not in content:
                missing_elements.append(element)

        if missing_elements:
            issues.append(f"缺少必要元素: {', '.join(missing_elements)}")
            quality_score -= len(missing_elements) * 0.5
        else:
            quality_score += 2.0

        # section特定检查
        section_score, section_issues = self._section_specific_quality_check(section, content)
        quality_score += section_score
        issues.extend(section_issues)

        # 语言质量检查
        language_score, language_issues = self._check_language_quality(content)
        quality_score += language_score
        issues.extend(language_issues)

        # 标准化评分到0-10范围
        final_score = max(0, min(10, quality_score + 5))  # 基础分5分

        return {
            'score': round(final_score, 2),
            'issues': issues,
            'details': details,
            'recommendations': self._generate_quality_recommendations(section, issues)
        }

    def _section_specific_quality_check(self, section: str, content: str) -> tuple:
        """section特定的质量检查"""
        score = 0.0
        issues = []

        if section == "claims":
            # 权利要求特定检查
            claim_count = len(re.findall(r'^\d+\.', content, re.MULTILINE))
            if claim_count == 0:
                issues.append("未找到有效的权利要求编号")
                score -= 2.0
            elif claim_count >= 1:
                score += 1.0

            # 检查独立权利要求
            if not re.search(r'1\.\s*.*特征在于', content):
                issues.append("第1项权利要求格式不规范")
                score -= 1.0
            else:
                score += 1.0

        elif section == "abstract":
            # 摘要特定检查
            if not any(keyword in content for keyword in ["发明", "方法", "系统", "装置"]):
                issues.append("摘要应明确说明发明的主题")
                score -= 0.5

            if "有益效果" not in content and "技术效果" not in content:
                issues.append("摘要应包含有益效果描述")
                score -= 0.5
            else:
                score += 1.0

        elif section == "background":
            # 背景技术特定检查
            if "现有技术" not in content and "背景技术" not in content:
                issues.append("应包含现有技术描述")
                score -= 1.0

            if not any(keyword in content for keyword in ["问题", "不足", "缺点", "局限"]):
                issues.append("应指出现有技术的问题或不足")
                score -= 0.5
            else:
                score += 1.0

        return score, issues

    def _check_language_quality(self, content: str) -> tuple:
        """检查语言质量"""
        score = 0.0
        issues = []

        # 检查重复词语
        words = content.split()
        word_freq = {}
        for word in words:
            if len(word) > 1:
                word_freq[word] = word_freq.get(word, 0) + 1

        repeated_words = [word for word, freq in word_freq.items() if freq > len(words) * 0.05]
        if repeated_words:
            issues.append(f"存在过度重复的词语: {', '.join(repeated_words[:3])}")
            score -= 0.5

        # 检查句子长度
        sentences = re.split(r'[。！？]', content)
        long_sentences = [s for s in sentences if len(s) > 200]
        if long_sentences:
            issues.append(f"存在过长的句子，建议分割")
            score -= 0.3

        # 检查专业术语一致性
        # 这里可以添加更复杂的术语检查逻辑

        return score, issues

    def _generate_quality_recommendations(self, section: str, issues: List[str]) -> List[str]:
        """基于问题生成改进建议"""
        recommendations = []

        for issue in issues:
            if "过短" in issue:
                recommendations.append(f"建议增加{section}的详细描述，补充更多技术细节")
            elif "过长" in issue:
                recommendations.append(f"建议精简{section}内容，去除冗余信息")
            elif "缺少必要元素" in issue:
                recommendations.append(f"请补充{section}中缺少的必要元素")
            elif "重复词语" in issue:
                recommendations.append("建议使用同义词替换重复词语，提高表达多样性")
            elif "句子" in issue:
                recommendations.append("建议将长句分解为多个短句，提高可读性")

        return recommendations

    def _optimize_content(self, section: str, content: str, issues: List[str]) -> Dict[str, Any]:
        """基于质量问题优化内容"""
        try:
            # 构建优化prompt
            optimization_prompt = f"""
请优化以下{section}内容，解决发现的问题：

原内容：
{content}

需要解决的问题：
{chr(10).join(f'- {issue}' for issue in issues)}

请生成优化后的内容，确保：
1. 解决上述问题
2. 保持原有的核心信息
3. 提高内容质量和规范性
4. 符合专利申请要求

优化后的{section}内容：
            """

            # 调用LLM进行优化
            optimized_content = self._generate_response(
                prompt=optimization_prompt,
                system_prompt=self._get_system_prompt(section, self.writer_type),
                temperature=0.5  # 降低随机性，确保优化的稳定性
            )

            # 后处理优化内容
            processed_content = self._post_process_content(section, optimized_content, {})

            return {
                'success': True,
                'content': processed_content,
                'optimization_applied': True
            }

        except Exception as e:
            self.logger.error(f"内容优化失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': content  # 返回原内容
            }

    def _update_pgtree(self,
                       pgtree: PGTreeHandler,
                       section: str,
                       content: str,
                       quality_score: float) -> PGTreeHandler:
        """更新PGTree中的节点内容"""
        try:
            # 更新节点状态和内容
            if hasattr(pgtree, 'update_node_content'):
                pgtree.update_node_content(
                    node_id=section,
                    content=content,
                    status=NodeStatus.COMPLETED
                )

            # 如果有nodes属性，直接更新
            if hasattr(pgtree, 'nodes') and section in pgtree.nodes:
                node = pgtree.nodes[section]
                if hasattr(node, 'content'):
                    node.content = content
                if hasattr(node, 'status'):
                    node.status = NodeStatus.COMPLETED
                if hasattr(node, 'quality_score'):
                    node.quality_score = quality_score
                if hasattr(node, 'word_count'):
                    node.word_count = len(content.split())
                if hasattr(node, 'updated_at'):
                    node.updated_at = datetime.now()

            return pgtree

        except Exception as e:
            self.logger.error(f"更新PGTree失败: {e}")
            return pgtree

    def _create_revision_prompt(self,
                                current_content: str,
                                feedback: str,
                                section: str,
                                requirements: Dict[str, Any]) -> str:
        """创建修订prompt"""
        return f"""
请根据以下反馈修订{section}内容：

当前内容：
{current_content}

修订要求：
{feedback}

修订重点：
{chr(10).join(f'- {req}' for req in requirements.get('focus_areas', []))}

请保持原有内容的核心信息，在此基础上进行改进和完善。

修订后的{section}内容：
        """

    def _get_revision_system_prompt(self, section: str) -> str:
        """获取修订专用的system prompt"""
        return f"""
你是一名专业的专利代理师，正在根据审查意见修订专利申请的{section}部分。

修订原则：
1. 仔细理解反馈意见的要求
2. 保持原有技术方案的核心内容
3. 改进表达方式和逻辑结构
4. 确保修订后内容符合专利申请规范
5. 提高内容的清晰度和准确性

请生成修订后的内容。
        """

    def _analyze_feedback(self, feedback: str) -> Dict[str, Any]:
        """分析反馈内容，提取修订要求"""
        requirements = {
            'focus_areas': [],
            'severity': 'medium',
            'type': 'general'
        }

        feedback_lower = feedback.lower()

        # 分析修订重点
        if '不清楚' in feedback or '模糊' in feedback:
            requirements['focus_areas'].append('提高表达清晰度')

        if '不完整' in feedback or '缺少' in feedback:
            requirements['focus_areas'].append('补充缺失内容')

        if '不准确' in feedback or '错误' in feedback:
            requirements['focus_areas'].append('修正技术描述')

        if '重复' in feedback:
            requirements['focus_areas'].append('消除重复内容')

        # 判断严重程度
        if any(keyword in feedback_lower for keyword in ['严重', '重大', '错误']):
            requirements['severity'] = 'high'
        elif any(keyword in feedback_lower for keyword in ['轻微', '建议', '优化']):
            requirements['severity'] = 'low'

        return requirements

    def _validate_revision(self,
                           original_content: str,
                           revised_content: str,
                           requirements: Dict[str, Any]) -> bool:
        """验证修订结果是否满足要求"""
        # 基础验证：修订后内容不能为空
        if not revised_content or not revised_content.strip():
            return False

        # 验证内容没有大幅缩减（保持核心信息）
        original_words = len(original_content.split())
        revised_words = len(revised_content.split())

        if revised_words < original_words * 0.5:  # 内容减少超过50%
            self.logger.warning("修订后内容大幅减少，可能丢失重要信息")
            return False

        # 验证关键技术词汇是否保留
        original_keywords = set(re.findall(r'\b\w{4,}\b', original_content))
        revised_keywords = set(re.findall(r'\b\w{4,}\b', revised_content))

        # 至少保留70%的关键词
        if len(revised_keywords.intersection(original_keywords)) < len(original_keywords) * 0.7:
            self.logger.warning("修订后内容丢失过多关键词")
            return False

        return True

    def _update_writing_stats(self,
                              section: str,
                              content: str,
                              processing_time: float,
                              quality_score: float):
        """更新写作统计信息"""
        self.writing_stats['total_sections_written'] += 1
        self.writing_stats['total_words_generated'] += len(content.split())

        # 更新平均写作时间
        total_time = (self.writing_stats['average_writing_time'] *
                      (self.writing_stats['total_sections_written'] - 1))
        self.writing_stats['average_writing_time'] = (
                (total_time + processing_time) / self.writing_stats['total_sections_written']
        )

        # 记录质量评分
        self.writing_stats['quality_scores'].append(quality_score)

    def _estimate_token_usage(self, context: Dict[str, Any], content: str) -> tuple:
        """估算token使用量"""
        # 简化的token估算
        context_text = str(context)

        # 中文字符按1个token，英文单词按0.75个token估算
        input_chars = len(context_text)
        input_tokens = int(input_chars * 0.8)  # 考虑prompt的复杂性

        output_chars = len(content)
        output_tokens = int(output_chars * 0.8)

        return input_tokens, output_tokens

    def _load_writing_guidelines(self) -> Dict[str, str]:
        """加载写作指南"""
        return {
            "abstract": "摘要应简洁明了地概述发明内容，包含技术领域、技术方案和有益效果",
            "background": "背景技术应客观描述相关领域现状，指出存在的技术问题",
            "summary": "发明内容应明确发明目的，详述技术方案，说明有益效果",
            "detailed_description": "具体实施方式应结合实施例详细说明发明的实现方法",
            "claims": "权利要求书应准确定义保护范围，逻辑清晰，层次分明"
        }

    def _load_content_templates(self) -> Dict[str, str]:
        """加载内容模板"""
        return {
            "abstract": ContentTemplate.ABSTRACT_TEMPLATE,
            "background": ContentTemplate.BACKGROUND_TEMPLATE,
            "claims": ContentTemplate.CLAIMS_TEMPLATE
        }

    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            'success': False,
            'error': error_message,
            'agent_name': self.agent_name,
            'timestamp': datetime.now().isoformat()
        }

    def get_writing_statistics(self) -> Dict[str, Any]:
        """获取写作统计信息"""
        stats = self.writing_stats.copy()

        # 计算平均质量评分
        if stats['quality_scores']:
            stats['average_quality_score'] = sum(stats['quality_scores']) / len(stats['quality_scores'])
        else:
            stats['average_quality_score'] = 0.0

        # 添加配置信息
        stats['writer_config'] = {
            'writer_type': self.writer_type,
            'writing_style': self.writing_style.value,
            'specialization': self.specialization_config.get(self.writer_type, {})
        }

        return stats

    def reset_statistics(self):
        """重置统计信息"""
        self.writing_stats = {
            "total_sections_written": 0,
            "total_words_generated": 0,
            "average_writing_time": 0.0,
            "revisions_made": 0,
            "quality_scores": []
        }

        self.logger.info(f"{self.agent_name} 统计信息已重置")

if __name__ == "__main__":
    # 创建不同类型的writer agent
    general_writer = WriterAgent("general", WritingStyle.FORMAL)
    technical_writer = WriterAgent("technical", WritingStyle.TECHNICAL)
    legal_writer = WriterAgent("legal", WritingStyle.LEGAL)

    # 写作请求
    input_data = {
        'pgtree': pgtree_instance,
        'section': 'abstract',
        'context': {
            'concept': '智能家居能耗管理系统',
            'options': {'technical_field': '智能家居技术'}
        },
        'workflow_id': 'wf_12345'
    }

    result = general_writer.process(input_data)
    if result['success']:
        print(f"质量评分: {result['quality_score']}")
        print(f"内容: {result['content']}")