"""
配置文件 - AutoPatent框架的配置设置
"""

import os
from typing import Dict, Any
from pydantic import SecretStr
from dotenv import load_dotenv


class AutoPatentConfig:
    """AutoPatent框架配置类"""

    def __init__(self):
        """初始化配置"""
        load_dotenv()

        # 大模型配置
        self.llm_config = {
            'base_url': 'https://api.deepseek.com/v1',
            'model': 'deepseek-chat',  # 或 deepseek-reasoner
            'api_key': self._load_api_key(),
            'temperature': 0.7,
            'max_tokens': 4000,
            'timeout': 60
        }

        # 数据库配置
        self.database_config = {
            'db_path': 'patents.db',
            'backup_enabled': True,
            'backup_interval': 3600  # 秒
        }

        # 工作流配置
        self.workflow_config = {
            'max_iterations': 3,
            'quality_threshold': 7.0,
            'enable_parallel_writing': False,
            'enable_rrag': True
        }

        # 专利结构配置
        self.patent_structure = {
            'sections': ['abstract', 'background', 'summary', 'detailed_description', 'claims'],
            'required_sections': ['background', 'summary', 'claims'],
            'section_dependencies': {
                'abstract': [],
                'background': [],
                'summary': ['background'],
                'detailed_description': ['summary'],
                'claims': ['detailed_description']
            }
        }

    def _load_api_key(self) -> SecretStr:
        """加载API密钥"""
        api_key = os.getenv('DEEPSEEK_API_KEY', '')
        if not api_key:
            raise ValueError('DEEPSEEK_API_KEY环境变量未设置')
        return SecretStr(api_key)

    def get_llm_config(self) -> Dict[str, Any]:
        """获取大模型配置"""
        return self.llm_config

    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.database_config

    def get_workflow_config(self) -> Dict[str, Any]:
        """获取工作流配置"""
        return self.workflow_config


# 全局配置实例
config = AutoPatentConfig()