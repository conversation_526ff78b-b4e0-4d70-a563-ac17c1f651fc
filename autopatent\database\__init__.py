"""
数据库模块 - Patent Database and RRAG Support

提供专利数据存储、检索和RRAG功能支持，
包括全文搜索、相似度计算和现有技术分析。
"""

from .patent_db import (
    PatentDB,
    SearchResult,
    PatentData,
    SearchConfig,
    SimilarityMethod
)

# 便捷函数
def create_patent_database(db_path: str = "patents.db",
                          enable_full_text_search: bool = True,
                          **kwargs) -> PatentDB:
    """
    创建专利数据库实例

    Args:
        db_path: 数据库文件路径
        enable_full_text_search: 是否启用全文搜索
        **kwargs: 其他配置参数

    Returns:
        配置好的专利数据库
    """
    return PatentDB(
        db_path=db_path,
        enable_full_text_search=enable_full_text_search,
        **kwargs
    )

def setup_rrag_database(db_path: str = "rrag_patents.db",
                       sample_patents: list = None) -> PatentDB:
    """
    设置RRAG专用数据库

    Args:
        db_path: 数据库路径
        sample_patents: 示例专利数据列表

    Returns:
        配置好的RRAG数据库
    """
    db = create_patent_database(
        db_path=db_path,
        enable_full_text_search=True,
        enable_similarity_search=True
    )

    # 添加示例数据
    if sample_patents:
        with db:
            for patent in sample_patents:
                db.add_patent(patent)

    return db

# 数据库工具函数
def import_patents_from_json(db: PatentDB, json_file: str) -> int:
    """
    从JSON文件导入专利数据

    Args:
        db: 数据库实例
        json_file: JSON文件路径

    Returns:
        导入的专利数量
    """
    import json

    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            patents = json.load(f)

        count = 0
        with db:
            for patent in patents:
                if db.add_patent(patent):
                    count += 1

        return count

    except Exception as e:
        print(f"导入专利数据失败: {e}")
        return 0

def export_patents_to_json(db: PatentDB, json_file: str, limit: int = None) -> bool:
    """
    导出专利数据到JSON文件

    Args:
        db: 数据库实例
        json_file: 输出JSON文件路径
        limit: 限制导出数量

    Returns:
        是否导出成功
    """
    import json

    try:
        patents = db.get_all_patents(limit=limit)

        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(patents, f, ensure_ascii=False, indent=2)

        return True

    except Exception as e:
        print(f"导出专利数据失败: {e}")
        return False

# 导出的公共接口
__all__ = [
    # 核心类
    "PatentDB",
    "SearchResult",
    "PatentData",
    "SearchConfig",
    "SimilarityMethod",

    # 便捷函数
    "create_patent_database",
    "setup_rrag_database",

    # 工具函数
    "import_patents_from_json",
    "export_patents_to_json",
]