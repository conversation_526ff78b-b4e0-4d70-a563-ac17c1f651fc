[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "autopatent"
version = "0.1.0"
description = "AI-powered patent generation system with multi-agent collaboration"
authors = [
    {name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
keywords = ["patent", "ai", "multi-agent", "automation", "nlp"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Legal Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Office/Business :: Legal",
]

dependencies = [
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "numpy>=1.24.0",
    "openai>=1.0.0",
    "httpx>=0.24.0",
    "aiohttp>=3.8.0",
    "requests>=2.28.0",
]

[project.optional-dependencies]
visualization = [
    "matplotlib>=3.5.0",
    "plotly>=5.0.0",
]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
]
all = [
    "autopatent[visualization,dev]"
]

[project.urls]
Homepage = "https://github.com/longwarriors/draftPatents"
Repository = "https://github.com/longwarriors/draftPatents.git"
Issues = "https://github.com/longwarriors/draftPatents/issues"

[project.scripts]
autopatent = "autopatent.cli:main"

[tool.hatch.build.targets.wheel]
packages = ["autopatent"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
