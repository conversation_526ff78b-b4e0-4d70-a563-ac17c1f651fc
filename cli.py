"""
AutoPatent 命令行接口
使用 typer 和 rich 提供现代化的CLI体验
"""

import typer
from typing import Optional, Dict, Any
from pathlib import Path
import json
import time
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax

from .coordinator.coordinator import AutoPatentCoordinator

# 创建typer应用和rich控制台
app = typer.Typer(
    name="autopatent",
    help="AutoPatent - 多智能体专利自动生成系统",
    add_completion=False,
    rich_markup_mode="rich"
)
console = Console()


@app.command()
def generate(
        concept: str = typer.Argument(..., help="专利概念描述"),
        field: str = typer.Option("通用技术领域", "--field", "-f", help="技术领域"),
        output: Optional[Path] = typer.Option(None, "--output", "-o", help="输出文件路径"),
        patent_type: str = typer.Option("invention", "--type", "-t",
                                        help="专利类型 (invention/utility_model/design)"),
        iterations: int = typer.Option(3, "--iterations", "-i", help="最大迭代次数"),
        quality: float = typer.Option(7.0, "--quality", "-q", help="质量阈值 (1-10)"),
        verbose: bool = typer.Option(False, "--verbose", "-v", help="详细输出")
):
    """生成专利文档"""

    # 显示欢迎信息
    console.print(Panel.fit(
        "[bold blue]AutoPatent[/bold blue]\n多智能体专利自动生成系统",
        border_style="blue"
    ))

    # 验证输入
    if not concept.strip():
        console.print("[red]错误：专利概念不能为空[/red]")
        raise typer.Exit(1)

    # 显示生成参数
    params_table = Table(title="生成参数")
    params_table.add_column("参数", style="cyan")
    params_table.add_column("值", style="green")

    params_table.add_row("专利概念", concept[:100] + "..." if len(concept) > 100 else concept)
    params_table.add_row("技术领域", field)
    params_table.add_row("专利类型", patent_type)
    params_table.add_row("最大迭代次数", str(iterations))
    params_table.add_row("质量阈值", str(quality))

    console.print(params_table)

    # 确认开始生成
    if not Confirm.ask("\n[yellow]开始生成专利？[/yellow]", default=True):
        console.print("[yellow]生成已取消[/yellow]")
        raise typer.Exit(0)

    # 准备生成选项
    options = {
        'technical_field': field,
        'patent_type': patent_type,
        'target_length': 'standard',
        'claim_count': {'independent': 2, 'dependent': 8}
    }

    # 初始化协调器
    coordinator = AutoPatentCoordinator(
        max_iterations=iterations,
        quality_threshold=quality
    )

    # 生成专利
    result = _generate_with_progress(coordinator, concept, options, verbose)

    # 处理结果
    if result['success']:
        _display_success_result(result, output)
    else:
        _display_error_result(result)


@app.command()
def interactive():
    """交互式模式"""
    console.print(Panel.fit(
        "[bold blue]AutoPatent 交互式模式[/bold blue]",
        border_style="blue"
    ))

    # 收集用户输入
    concept = Prompt.ask("\n[cyan]请输入专利概念[/cyan]")
    if not concept.strip():
        console.print("[red]错误：专利概念不能为空[/red]")
        raise typer.Exit(1)

    field = Prompt.ask("[cyan]技术领域[/cyan]", default="通用技术领域")

    # 专利类型选择
    patent_types = {
        "1": ("invention", "发明专利"),
        "2": ("utility_model", "实用新型专利"),
        "3": ("design", "外观设计专利")
    }

    console.print("\n[cyan]专利类型：[/cyan]")
    for key, (_, name) in patent_types.items():
        console.print(f"  {key}. {name}")

    type_choice = Prompt.ask("选择专利类型", choices=list(patent_types.keys()), default="1")
    patent_type, type_name = patent_types[type_choice]

    # 高级选项
    advanced = Confirm.ask("\n[cyan]配置高级选项？[/cyan]", default=False)

    iterations = 3
    quality = 7.0
    independent_claims = 2
    dependent_claims = 8

    if advanced:
        iterations = typer.prompt("最大迭代次数", default=3, type=int)
        quality = typer.prompt("质量阈值 (1-10)", default=7.0, type=float)
        independent_claims = typer.prompt("独立权利要求数量", default=2, type=int)
        dependent_claims = typer.prompt("从属权利要求数量", default=8, type=int)

    # 显示配置摘要
    config_table = Table(title="配置摘要")
    config_table.add_column("项目", style="cyan")
    config_table.add_column("值", style="green")

    config_table.add_row("专利概念", concept[:50] + "..." if len(concept) > 50 else concept)
    config_table.add_row("技术领域", field)
    config_table.add_row("专利类型", type_name)
    config_table.add_row("最大迭代", str(iterations))
    config_table.add_row("质量阈值", str(quality))
    config_table.add_row("权利要求", f"独立{independent_claims}项，从属{dependent_claims}项")

    console.print(config_table)

    if not Confirm.ask("\n[yellow]确认生成？[/yellow]", default=True):
        console.print("[yellow]生成已取消[/yellow]")
        raise typer.Exit(0)

    # 准备选项
    options = {
        'technical_field': field,
        'patent_type': patent_type,
        'target_length': 'standard',
        'claim_count': {'independent': independent_claims, 'dependent': dependent_claims}
    }

    # 生成专利
    coordinator = AutoPatentCoordinator(
        max_iterations=iterations,
        quality_threshold=quality
    )

    result = _generate_with_progress(coordinator, concept, options, verbose=True)

    # 处理结果
    if result['success']:
        # 询问是否保存
        if Confirm.ask("\n[cyan]保存结果到文件？[/cyan]", default=True):
            filename = f"patent_{int(time.time())}.json"
            output_path = Path(filename)
            _save_result(result, output_path)

        _display_success_result(result)
    else:
        _display_error_result(result)


@app.command()
def status():
    """显示系统状态"""
    console.print(Panel.fit("[bold blue]AutoPatent 系统状态[/bold blue]", border_style="blue"))

    # 检查环境
    status_table = Table(title="环境检查")
    status_table.add_column("项目", style="cyan")
    status_table.add_column("状态", style="green")
    status_table.add_column("详情", style="yellow")

    # Python版本
    import sys
    python_ok = sys.version_info >= (3, 8)
    status_table.add_row(
        "Python版本",
        "✓" if python_ok else "✗",
        f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    )

    # API密钥
    import os
    api_key_ok = bool(os.getenv('DEEPSEEK_API_KEY'))
    status_table.add_row(
        "DEEPSEEK_API_KEY",
        "✓" if api_key_ok else "✗",
        "已设置" if api_key_ok else "未设置"
    )

    # 依赖包
    required_packages = ['langchain_openai', 'pydantic', 'python-dotenv', 'numpy', 'rich', 'typer']
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            status_table.add_row(package, "✓", "已安装")
        except ImportError:
            status_table.add_row(package, "✗", "未安装")

    console.print(status_table)

    # 数据库状态
    try:
        from .database.patent_db import PatentDB
        db = PatentDB()
        stats = db.get_statistics()

        db_table = Table(title="数据库状态")
        db_table.add_column("项目", style="cyan")
        db_table.add_column("值", style="green")

        db_table.add_row("专利总数", str(stats.get('total_patents', 0)))
        db_table.add_row("技术领域数", str(stats.get('technical_fields', 0)))

        console.print(db_table)
    except Exception as e:
        console.print(f"[red]数据库检查失败: {e}[/red]")


@app.command()
def search(
        query: str = typer.Argument(..., help="搜索关键词"),
        limit: int = typer.Option(10, "--limit", "-l", help="结果数量限制")
):
    """搜索专利数据库"""
    from .database.patent_db import PatentDB

    console.print(f"[cyan]搜索专利：{query}[/cyan]")

    try:
        db = PatentDB()
        results = db.search_patents(query, limit=limit)

        if not results:
            console.print("[yellow]未找到匹配的专利[/yellow]")
            return

        # 显示搜索结果
        results_table = Table(title=f"搜索结果 (共{len(results)}项)")
        results_table.add_column("专利号", style="cyan")
        results_table.add_column("标题", style="green")
        results_table.add_column("技术领域", style="yellow")

        for result in results:
            results_table.add_row(
                result.get('patent_id', 'N/A'),
                result.get('title', 'N/A')[:50] + "..." if len(result.get('title', '')) > 50 else result.get('title',
                                                                                                             'N/A'),
                result.get('technical_field', 'N/A')
            )

        console.print(results_table)

    except Exception as e:
        console.print(f"[red]搜索失败: {e}[/red]")


def _generate_with_progress(coordinator: AutoPatentCoordinator,
                            concept: str,
                            options: Dict[str, Any],
                            verbose: bool = False) -> Dict[str, Any]:
    """带进度显示的生成专利"""

    with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
            transient=True
    ) as progress:
        task = progress.add_task("[cyan]正在生成专利...", total=None)

        if verbose:
            # 详细模式下显示各阶段
            progress.update(task, description="[cyan]初始化系统...")
            time.sleep(1)

            progress.update(task, description="[cyan]规划阶段...")
            time.sleep(1)

            progress.update(task, description="[cyan]撰写阶段...")
            time.sleep(1)

            progress.update(task, description="[cyan]审查阶段...")
            time.sleep(1)

        # 实际生成
        result = coordinator.generate_patent(concept, options)

        progress.update(task, description="[green]生成完成!")

    return result


def _display_success_result(result: Dict[str, Any], output_path: Optional[Path] = None):
    """显示成功结果"""
    console.print("\n[green]✓ 专利生成成功！[/green]")

    # 显示统计信息
    stats = result.get('statistics', {})
    token_usage = result.get('token_usage', {}).get('total', {})

    info_table = Table(title="生成统计")
    info_table.add_column("项目", style="cyan")
    info_table.add_column("值", style="green")

    info_table.add_row("工作流状态", result.get('workflow_state', 'N/A'))
    info_table.add_row("迭代次数", str(stats.get('iterations', 0)))
    info_table.add_row("输入令牌", str(token_usage.get('input_tokens', 0)))
    info_table.add_row("输出令牌", str(token_usage.get('output_tokens', 0)))
    info_table.add_row("总令牌", str(token_usage.get('total_tokens', 0)))

    console.print(info_table)

    # 显示专利内容预览
    patent_content = result.get('patent_content', {})

    for section, content in patent_content.items():
        section_title = section.replace('_', ' ').title()
        preview = content[:200] + "..." if len(content) > 200 else content

        panel = Panel(
            preview,
            title=f"[bold]{section_title}[/bold]",
            border_style="blue"
        )
        console.print(panel)

    # 保存文件
    if output_path:
        _save_result(result, output_path)


def _display_error_result(result: Dict[str, Any]):
    """显示错误结果"""
    console.print("\n[red]✗ 专利生成失败[/red]")
    console.print(f"[red]错误：{result.get('error', '未知错误')}[/red]")

    if 'details' in result:
        console.print(f"[yellow]详情：{result['details']}[/yellow]")


def _save_result(result: Dict[str, Any], output_path: Path):
    """保存结果到文件"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        console.print(f"[green]✓ 结果已保存到：{output_path}[/green]")
    except Exception as e:
        console.print(f"[red]保存失败：{e}[/red]")


@app.command(name="db-stats", help="显示专利数据库的统计信息。")
def show_db_stats():
    """显示专利数据库统计信息"""
    console.print(Panel.fit("[bold blue]专利数据库统计信息[/bold blue]", border_style="blue"))

    try:
        from .database.patent_db import PatentDB
        db = PatentDB()
        stats = db.get_db_metrics()
        if stats:
            db_table = Table(title="专利数据库统计")
            db_table.add_column("指标", style="cyan")
            db_table.add_column("值", style="magenta")

            # 尝试使用属性访问，如果属性可能不存在，则使用 getattr
            total_patents = getattr(stats, 'total_patents', 0)
            technical_fields = getattr(stats, 'technical_fields', 0)

            db_table.add_row("专利总数", str(total_patents))
            db_table.add_row("技术领域数", str(technical_fields))
            # ... 您可能还有其他指标 ...
            console.print(db_table)
        else:
            console.print("[yellow]未获取到统计信息[/yellow]")
    except Exception as e:
        console.print(f"[red]获取统计信息失败: {e}[/red]")


def generate_command():
    """简化的生成命令入口"""
    app()


def main():
    """主入口函数"""
    app()


if __name__ == "__main__":
    main()