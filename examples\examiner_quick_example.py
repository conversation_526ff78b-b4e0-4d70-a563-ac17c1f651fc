"""
ExaminerAgent 快速使用示例
"""

from patents_generate.agents.examiner_agent import ExaminerAgent
from patents_generate.database.patent_db import PatentDB
from patents_generate.utils.pg_tree_handler import PGTreeHandler


def quick_example():
    """快速使用示例"""

    # 创建数据库和PGTree（实际使用中这些应该已经存在）
    with PatentDB("quick_example.db") as patent_db:
        # 初始化examiner
        examiner = ExaminerAgent(patent_db, enable_rrag=True)

        # 创建简单的PGTree用于测试
        pgtree = PGTreeHandler()

        # 模拟添加一个已完成的section
        from patents_generate.utils.pg_tree_handler import PGTreeNode, NodeStatus

        node = PGTreeNode(
            node_id="abstract",
            section_name="abstract",
            content="本发明涉及一种智能设备控制系统...",
            status=NodeStatus.COMPLETED
        )
        pgtree.add_node(node)

        # 执行审查
        result = examiner.process({
            'pgtree': pgtree,
            'section': 'abstract',
            'examination_type': 'substantive'
        })

        # 查看结果
        if result['success']:
            print(f"审查评分: {result['overall_score']}")
            print(f"发现 {len(result['issues'])} 个问题")
        else:
            print(f"审查失败: {result['error']}")


if __name__ == "__main__":
    quick_example()