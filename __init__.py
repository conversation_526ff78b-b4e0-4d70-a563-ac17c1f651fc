"""
draftPatent - 智能专利生成系统

基于多智能体协作的专利申请文档自动生成系统，
支持RRAG检索增强审查和智能化工作流管理。
"""

__version__ = "0.0.1"
__author__ = "<PERSON><PERSON><PERSON><PERSON>"
__email__ = "<EMAIL>"
__description__ = "AI-powered patent generation system with multi-agent collaboration"

# 导入核心组件
from .coordinator.coordinator import Coordinator
from .coordinator.workflow_manager import WorkflowManager

# 导入智能体
from .agents.base_agent import BaseAgent, AgentStatus, AgentCapability
from .agents.planner_agent import PlannerAgent, PlanningStrategy, PatentType
from .agents.writer_agent import WriterAgent, WriterType, WritingStyle
from .agents.examiner_agent import ExaminerAgent, ExaminationType, IssueType

# 导入工具类
from .utils.token_tracker import TokenTracker, OperationType
from .utils.token_visualizer import TokenVisualizer
from .utils.pg_tree_handler import PG<PERSON>reeHand<PERSON>, PGTreeNode, NodeStatus
from .utils.llm_client import LLMClient, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ModelProvider, ModelConfig

# 导入数据库
from .database.patent_db import PatentDB

# 导出主要类和枚举
__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__description__",

    # 核心组件
    "Coordinator",
    "WorkflowManager",

    # 智能体
    "BaseAgent",
    "PlannerAgent",
    "WriterAgent",
    "ExaminerAgent",

    # 智能体相关枚举
    "AgentStatus",
    "AgentCapability",
    "PlanningStrategy",
    "PatentType",
    "WriterType",
    "WritingStyle",
    "ExaminationType",
    "IssueType",

    # 工具类
    "TokenTracker",
    "TokenVisualizer",
    "PGTreeHandler",
    "PGTreeNode",
    "LLMClient",
    "LLMClientManager",

    # 工具类相关枚举
    "OperationType",
    "NodeStatus",
    "ModelProvider",
    "ModelConfig",

    # 数据库
    "PatentDB",
]

# 配置日志
import logging


def setup_logging(level: str = "INFO",
                  log_file: str = None,
                  format_string: str = None) -> None:
    """
    设置AutoPatent系统的日志配置

    Args:
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径，None表示只输出到控制台
        format_string: 自定义日志格式
    """
    if format_string is None:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # 创建formatter
    formatter = logging.Formatter(format_string)

    # 设置根logger级别
    root_logger = logging.getLogger("AutoPatent")
    root_logger.setLevel(getattr(logging, level.upper()))

    # 清除现有handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 添加控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 添加文件handler（如果指定）
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    root_logger.info(f"AutoPatent日志系统已初始化 - 级别: {level}")


def get_version_info() -> dict:
    """获取版本信息"""
    return {
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "python_requires": ">=3.8",
        "dependencies": [
            "openai>=1.0.0",
            "aiohttp>=3.8.0",
            "httpx>=0.24.0",
            "sqlite3",
            "pandas>=1.5.0",
            "matplotlib>=3.5.0",
            "plotly>=5.0.0"
        ]
    }


# 默认设置基础日志
setup_logging()