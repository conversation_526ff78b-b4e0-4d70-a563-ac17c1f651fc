"""
示例模块 - Examples and Tutorials

提供完整的使用示例、教程和最佳实践，
帮助用户快速上手AutoPatent系统。
"""

# 示例列表
AVAILABLE_EXAMPLES = {
    'examiner_agent_example': '完整的ExaminerAgent使用示例',
    'examiner_quick_example': 'ExaminerAgent快速使用示例',
    'basic_patent_generation': '基础专利生成示例',
    'multi_agent_coordination': '多智能体协调示例',
    'rrag_examination': 'RRAG检索增强审查示例',
    'token_tracking_demo': 'Token追踪和可视化示例',
    'workflow_management': '工作流管理示例'
}


def list_examples() -> dict:
    """
    列出所有可用示例

    Returns:
        示例字典 {name: description}
    """
    return AVAILABLE_EXAMPLES.copy()


def run_example(example_name: str, **kwargs):
    """
    运行指定示例

    Args:
        example_name: 示例名称
        **kwargs: 示例参数
    """
    if example_name not in AVAILABLE_EXAMPLES:
        print(f"示例 '{example_name}' 不存在")
        print(f"可用示例: {list(AVAILABLE_EXAMPLES.keys())}")
        return

    try:
        if example_name == 'examiner_agent_example':
            from .examiner_agent_example import main
            main()
        elif example_name == 'examiner_quick_example':
            from .examiner_quick_example import quick_example
            quick_example()
        else:
            print(f"示例 '{example_name}' 暂未实现")

    except ImportError as e:
        print(f"导入示例失败: {e}")
    except Exception as e:
        print(f"运行示例失败: {e}")


def get_example_help(example_name: str) -> str:
    """
    获取示例帮助信息

    Args:
        example_name: 示例名称

    Returns:
        帮助文档字符串
    """
    help_docs = {
        'examiner_agent_example': """
ExaminerAgent完整示例

这个示例展示了如何使用ExaminerAgent进行专利审查：
1. 创建示例数据库和PGTree
2. 初始化ExaminerAgent
3. 执行全面审查和单个section审查
4. 查看审查结果和统计信息

使用方法:
    from patents_generate.examples import run_example
    run_example('examiner_agent_example')
        """,

        'examiner_quick_example': """
ExaminerAgent快速示例

这是一个简化的ExaminerAgent使用示例：
1. 快速创建测试环境
2. 基本的审查功能演示
3. 结果查看

使用方法:
    from patents_generate.examples import run_example
    run_example('examiner_quick_example')
        """
    }

    return help_docs.get(example_name, f"示例 '{example_name}' 暂无帮助文档")


# 导出的公共接口
__all__ = [
    "AVAILABLE_EXAMPLES",
    "list_examples",
    "run_example",
    "get_example_help",
]