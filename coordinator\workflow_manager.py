"""
Workflow管理器 - 管理复杂的专利生成工作流程
"""

from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import asyncio
import logging
import uuid

from ..utils.pg_tree_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NodeStatus
from ..agents.planner_agent import PlannerAgent
from ..agents.writer_agent import WriterAgent
from ..agents.examiner_agent import ExaminerAgent


class WorkflowStage(Enum):
    """Workflow阶段枚举"""
    INITIALIZATION = "initialization"  # 初始化
    PLANNING = "planning"  # 规划
    WRITING = "writing"  # 写作
    REVIEWING = "reviewing"  # 审查
    REVISION = "revision"  # 修订
    QUALITY_CHECK = "quality_check"  # 质量检查
    FINALIZATION = "finalization"  # 最终化
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败


class TaskPriority(Enum):
    """任务优先级枚举"""
    CRITICAL = 1  # 关键
    HIGH = 2  # 高
    MEDIUM = 3  # 中
    LOW = 4  # 低


@dataclass
class WorkflowTask:
    """Workflow任务数据类"""
    task_id: str
    stage: WorkflowStage
    node_id: str
    agent_type: str
    priority: TaskPriority
    dependencies: List[str] = field(default_factory=list)
    max_retries: int = 3
    retry_count: int = 0
    estimated_duration: timedelta = field(default_factory=lambda: timedelta(minutes=10))

    # 状态tracking
    status: str = "pending"  # pending, running, completed, failed
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

    # 结果存储
    result: Optional[Dict[str, Any]] = None


class WorkflowManager:
    """高级workflow管理器"""

    def __init__(self,
                 max_concurrent_tasks: int = 3,
                 quality_threshold: float = 7.0,
                 max_iterations: int = 5):
        """
        初始化Workflow管理器

        Args:
            max_concurrent_tasks: 最大并发任务数
            quality_threshold: 质量阈值
            max_iterations: 最大迭代次数
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self.quality_threshold = quality_threshold
        self.max_iterations = max_iterations

        # 核心组件
        self.logger = logging.getLogger("AutoPatent.WorkflowManager")
        self.pg_tree_handler = PGTreeHandler()

        # Agent池
        self.agents = {
            'planner': PlannerAgent(),
            'writer_general': WriterAgent('general'),
            'writer_technical': WriterAgent('technical'),
            'writer_legal': WriterAgent('legal'),
            'examiner': None  # 需要patent_db初始化
        }

        # 任务管理
        self.task_queue: List[WorkflowTask] = []
        self.running_tasks: Dict[str, WorkflowTask] = {}
        self.completed_tasks: Dict[str, WorkflowTask] = {}
        self.failed_tasks: Dict[str, WorkflowTask] = {}

        # Workflow状态
        self.current_stage = WorkflowStage.INITIALIZATION
        self.workflow_id = f"wf_{uuid.uuid4().hex[:8]}"
        self.started_at = datetime.now()
        self.current_iteration = 0

        # 回调系统
        self.stage_callbacks: Dict[WorkflowStage, List[Callable]] = {}
        self.task_callbacks: Dict[str, List[Callable]] = {}

        # 性能监控
        self.stage_durations: Dict[str, float] = {}
        self.task_durations: Dict[str, float] = {}

        self.logger.info(f"WorkflowManager初始化完成，workflow_id: {self.workflow_id}")

    def initialize_workflow(self,
                            concept: str,
                            options: Dict[str, Any],
                            patent_db=None) -> Dict[str, Any]:
        """
        初始化workflow执行环境

        Args:
            concept: 专利概念
            options: 生成选项
            patent_db: 专利数据库实例

        Returns:
            初始化结果
        """
        self.logger.info(f"初始化workflow {self.workflow_id}")

        try:
            # 初始化examiner agent（需要patent_db）
            if patent_db and not self.agents['examiner']:
                from ..agents.examiner_agent import ExaminerAgent
                self.agents['examiner'] = ExaminerAgent(patent_db)

            # 创建PGTree
            template_type = options.get('patent_type', 'standard_invention')
            result = self.pg_tree_handler.create_from_concept(concept, template_type)

            if not result['success']:
                return {"success": False, "error": "PGTree创建失败"}

            # 生成task执行计划
            self._generate_task_plan(concept, options)

            # 更新状态
            self.current_stage = WorkflowStage.PLANNING

            self.logger.info(f"Workflow初始化成功，生成 {len(self.task_queue)} 个任务")

            return {
                "success": True,
                "workflow_id": self.workflow_id,
                "total_tasks": len(self.task_queue),
                "pg_tree_stats": result,
                "next_stage": self.current_stage.value
            }

        except Exception as e:
            self.logger.error(f"Workflow初始化失败: {e}")
            return {"success": False, "error": str(e)}

    async def execute_workflow_async(self) -> Dict[str, Any]:
        """
        异步执行完整workflow

        Returns:
            执行结果
        """
        self.logger.info(f"开始异步执行workflow {self.workflow_id}")

        try:
            while (self.current_stage not in [WorkflowStage.COMPLETED, WorkflowStage.FAILED] and
                   self.current_iteration < self.max_iterations):

                # 执行当前阶段
                stage_result = await self._execute_stage_async()

                if not stage_result['success']:
                    self.current_stage = WorkflowStage.FAILED
                    break

                # 检查是否需要迭代
                if self._needs_iteration():
                    self.current_iteration += 1
                    self.current_stage = WorkflowStage.WRITING  # 回到写作阶段
                    self._generate_revision_tasks()
                    self.logger.info(f"开始第 {self.current_iteration} 轮迭代")
                else:
                    # 移动到下一阶段
                    self._advance_to_next_stage()

            # 生成最终结果
            return self._generate_final_result()

        except Exception as e:
            self.logger.error(f"Workflow执行失败: {e}")
            self.current_stage = WorkflowStage.FAILED
            return {"success": False, "error": str(e)}

    async def _execute_stage_async(self) -> Dict[str, Any]:
        """异步执行当前阶段的任务"""
        stage_name = self.current_stage.value
        self.logger.info(f"执行阶段: {stage_name}")

        stage_start_time = datetime.now()

        # 触发阶段开始回调
        await self._trigger_stage_callbacks(self.current_stage, "start")

        # 获取当前阶段的pending任务
        stage_tasks = [task for task in self.task_queue
                       if task.stage == self.current_stage and task.status == "pending"]

        if not stage_tasks:
            # 没有任务，直接完成
            await self._trigger_stage_callbacks(self.current_stage, "complete")
            return {"success": True, "tasks_completed": 0}

        # 执行任务（批量并发）
        completed_count = 0
        failed_count = 0

        # 按优先级排序
        stage_tasks.sort(key=lambda x: x.priority.value)

        # 分批执行（考虑并发限制）
        for i in range(0, len(stage_tasks), self.max_concurrent_tasks):
            batch = stage_tasks[i:i + self.max_concurrent_tasks]

            # 并发执行这一批任务
            batch_results = await asyncio.gather(
                *[self._execute_task_async(task) for task in batch],
                return_exceptions=True
            )

            # 处理批次结果
            for task, result in zip(batch, batch_results):
                if isinstance(result, Exception):
                    self.logger.error(f"任务 {task.task_id} 异常: {result}")
                    failed_count += 1
                    task.status = "failed"
                    task.error_message = str(result)
                    self.failed_tasks[task.task_id] = task
                elif result.get('success'):
                    completed_count += 1
                    task.status = "completed"
                    task.result = result
                    task.completed_at = datetime.now()
                    self.completed_tasks[task.task_id] = task
                else:
                    failed_count += 1
                    task.status = "failed"
                    task.error_message = result.get('error', 'Unknown error')
                    self.failed_tasks[task.task_id] = task

        # 记录阶段持续时间
        stage_duration = (datetime.now() - stage_start_time).total_seconds()
        self.stage_durations[stage_name] = stage_duration

        # 触发阶段完成回调
        await self._trigger_stage_callbacks(self.current_stage, "complete")

        # 判断阶段是否成功
        success_rate = completed_count / len(stage_tasks) if stage_tasks else 1.0
        stage_success = success_rate > 0.5  # 至少50%任务成功

        self.logger.info(
            f"阶段 {stage_name} 完成: {completed_count} 成功, {failed_count} 失败, 耗时 {stage_duration:.2f}秒")

        return {
            "success": stage_success,
            "stage": stage_name,
            "tasks_completed": completed_count,
            "tasks_failed": failed_count,
            "total_tasks": len(stage_tasks),
            "success_rate": success_rate,
            "duration": stage_duration
        }

    async def _execute_task_async(self, task: WorkflowTask) -> Dict[str, Any]:
        """异步执行单个任务"""
        task_start_time = datetime.now()
        self.logger.debug(f"执行任务: {task.task_id}")

        task.status = "running"
        task.started_at = task_start_time
        self.running_tasks[task.task_id] = task

        try:
            # 根据agent类型选择执行器
            if task.agent_type == "planner":
                result = await self._execute_planner_task_async(task)
            elif task.agent_type.startswith("writer"):
                result = await self._execute_writer_task_async(task)
            elif task.agent_type == "examiner":
                result = await self._execute_examiner_task_async(task)
            else:
                raise ValueError(f"未知的agent类型: {task.agent_type}")

            # 记录任务持续时间
            task_duration = (datetime.now() - task_start_time).total_seconds()
            self.task_durations[task.task_id] = task_duration

            # 从running任务中移除
            if task.task_id in self.running_tasks:
                del self.running_tasks[task.task_id]

            self.logger.debug(f"任务 {task.task_id} 完成，耗时 {task_duration:.2f}秒")

            return result

        except Exception as e:
            self.logger.error(f"任务 {task.task_id} 执行失败: {e}")

            # 重试逻辑
            task.retry_count += 1
            if task.retry_count < task.max_retries:
                self.logger.info(f"重试任务 {task.task_id} (第{task.retry_count}次)")
                task.status = "pending"
                # 指数退避
                await asyncio.sleep(2 ** (task.retry_count - 1))
                return await self._execute_task_async(task)
            else:
                return {"success": False, "error": str(e)}

    async def _execute_planner_task_async(self, task: WorkflowTask) -> Dict[str, Any]:
        """异步执行规划任务"""
        # 模拟异步规划处理
        await asyncio.sleep(0.1)  # 模拟异步操作

        agent = self.agents['planner']
        input_data = {
            'concept': getattr(self, 'current_concept', ''),
            'options': getattr(self, 'current_options', {}),
            'task_id': task.task_id
        }

        # 实际调用agent（可能需要包装为async）
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(None, agent.process, input_data)

        return result

    async def _execute_writer_task_async(self, task: WorkflowTask) -> Dict[str, Any]:
        """异步执行写作任务"""
        await asyncio.sleep(0.1)

        # 选择合适的writer agent
        if task.agent_type == "writer_legal":
            agent = self.agents['writer_legal']
        elif task.agent_type == "writer_technical":
            agent = self.agents['writer_technical']
        else:
            agent = self.agents['writer_general']

        input_data = {
            'pgtree': self.pg_tree_handler,
            'section': task.node_id,
            'context': {},
            'task_id': task.task_id
        }

        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(None, agent.process, input_data)

        return result

    async def _execute_examiner_task_async(self, task: WorkflowTask) -> Dict[str, Any]:
        """异步执行审查任务"""
        await asyncio.sleep(0.1)

        if not self.agents['examiner']:
            return {"success": False, "error": "ExaminerAgent未初始化"}

        agent = self.agents['examiner']
        input_data = {
            'pgtree': self.pg_tree_handler,
            'section': task.node_id,
            'task_id': task.task_id
        }

        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(None, agent.process, input_data)

        return result

    def _generate_task_plan(self, concept: str, options: Dict[str, Any]):
        """生成详细的任务执行计划"""
        self.task_queue.clear()

        # 保存context
        self.current_concept = concept
        self.current_options = options

        # 生成规划任务
        planning_task = WorkflowTask(
            task_id=f"plan_{self.workflow_id}",
            stage=WorkflowStage.PLANNING,
            node_id="root",
            agent_type="planner",
            priority=TaskPriority.CRITICAL,
            estimated_duration=timedelta(minutes=5)
        )
        self.task_queue.append(planning_task)

        # 为每个PGTree节点生成写作任务
        for node_id, node_data in self.pg_tree_handler.nodes.items():
            # 确定agent类型
            agent_type = self._determine_agent_type(node_data.section_name)

            writing_task = WorkflowTask(
                task_id=f"write_{node_id}_{self.workflow_id}",
                stage=WorkflowStage.WRITING,
                node_id=node_id,
                agent_type=agent_type,
                priority=TaskPriority.HIGH,
                dependencies=[planning_task.task_id],
                estimated_duration=timedelta(minutes=10)
            )
            self.task_queue.append(writing_task)

            # 生成审查任务
            review_task = WorkflowTask(
                task_id=f"review_{node_id}_{self.workflow_id}",
                stage=WorkflowStage.REVIEWING,
                node_id=node_id,
                agent_type="examiner",
                priority=TaskPriority.MEDIUM,
                dependencies=[writing_task.task_id],
                estimated_duration=timedelta(minutes=8)
            )
            self.task_queue.append(review_task)

        self.logger.info(f"生成了 {len(self.task_queue)} 个任务")

    def _determine_agent_type(self, section_name: str) -> str:
        """根据section名称确定agent类型"""
        if section_name in ['claims']:
            return "writer_legal"
        elif section_name in ['detailed_description', 'background']:
            return "writer_technical"
        else:
            return "writer_general"

    def _needs_iteration(self) -> bool:
        """检查是否需要迭代"""
        # 检查质量评分
        quality_analysis = self.pg_tree_handler.get_quality_analysis()
        avg_quality = quality_analysis.get('average_quality', 0)

        return avg_quality < self.quality_threshold and self.current_iteration < self.max_iterations

    def _generate_revision_tasks(self):
        """生成修订任务"""
        quality_analysis = self.pg_tree_handler.get_quality_analysis()
        needs_improvement = quality_analysis.get('needs_improvement', [])

        for item in needs_improvement:
            revision_task = WorkflowTask(
                task_id=f"revise_{item['node_id']}_{self.current_iteration}_{self.workflow_id}",
                stage=WorkflowStage.REVISION,
                node_id=item['node_id'],
                agent_type=self._determine_agent_type(item.get('section', '')),
                priority=TaskPriority.HIGH,
                estimated_duration=timedelta(minutes=6)
            )
            self.task_queue.append(revision_task)

    def _advance_to_next_stage(self):
        """移动到下一个workflow阶段"""
        stage_order = [
            WorkflowStage.INITIALIZATION,
            WorkflowStage.PLANNING,
            WorkflowStage.WRITING,
            WorkflowStage.REVIEWING,
            WorkflowStage.QUALITY_CHECK,
            WorkflowStage.FINALIZATION,
            WorkflowStage.COMPLETED
        ]

        try:
            current_index = stage_order.index(self.current_stage)
            if current_index < len(stage_order) - 1:
                self.current_stage = stage_order[current_index + 1]
                self.logger.info(f"移动到阶段: {self.current_stage.value}")
        except ValueError:
            self.logger.warning(f"未知的当前阶段: {self.current_stage}")

    async def _trigger_stage_callbacks(self, stage: WorkflowStage, event: str):
        """触发阶段回调函数"""
        if stage in self.stage_callbacks:
            for callback in self.stage_callbacks[stage]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(stage, event, self)
                    else:
                        callback(stage, event, self)
                except Exception as e:
                    self.logger.error(f"回调执行失败: {e}")

    def _generate_final_result(self) -> Dict[str, Any]:
        """生成最终workflow结果"""
        completion_time = datetime.now()
        total_duration = (completion_time - self.started_at).total_seconds()

        # 收集PGTree进度和质量信息
        progress = self.pg_tree_handler.get_completion_progress()
        quality = self.pg_tree_handler.get_quality_analysis()

        # 提取专利content
        patent_content = {}
        for node_id, node in self.pg_tree_handler.nodes.items():
            if node.content:
                patent_content[node.section_name] = node.content

        # 任务统计
        task_stats = {
            "total_tasks": len(self.task_queue),
            "completed_tasks": len(self.completed_tasks),
            "failed_tasks": len(self.failed_tasks),
            "success_rate": len(self.completed_tasks) / len(self.task_queue) if self.task_queue else 0
        }

        return {
            "success": self.current_stage == WorkflowStage.COMPLETED,
            "workflow_id": self.workflow_id,
            "final_stage": self.current_stage.value,
            "iterations": self.current_iteration,
            "total_duration_seconds": total_duration,
            "started_at": self.started_at.isoformat(),
            "completed_at": completion_time.isoformat(),
            "progress": progress,
            "quality": quality,
            "patent_content": patent_content,
            "task_statistics": task_stats,
            "stage_durations": self.stage_durations,
            "performance_summary": self._generate_performance_summary()
        }

    def _generate_performance_summary(self) -> Dict[str, Any]:
        """生成性能摘要"""
        return {
            "average_task_duration": sum(self.task_durations.values()) / len(
                self.task_durations) if self.task_durations else 0,
            "slowest_stage": max(self.stage_durations.items(), key=lambda x: x[1]) if self.stage_durations else None,
            "fastest_stage": min(self.stage_durations.items(), key=lambda x: x[1]) if self.stage_durations else None,
            "total_stages": len(self.stage_durations),
            "concurrent_efficiency": len(self.completed_tasks) / max(self.max_concurrent_tasks, 1)
        }

    def add_stage_callback(self, stage: WorkflowStage, callback: Callable):
        """添加阶段回调函数"""
        if stage not in self.stage_callbacks:
            self.stage_callbacks[stage] = []
        self.stage_callbacks[stage].append(callback)

    def get_workflow_status(self) -> Dict[str, Any]:
        """获取详细的workflow状态"""
        current_time = datetime.now()
        elapsed_time = (current_time - self.started_at).total_seconds()

        return {
            "workflow_id": self.workflow_id,
            "current_stage": self.current_stage.value,
            "iteration": self.current_iteration,
            "elapsed_seconds": elapsed_time,
            "progress": self.pg_tree_handler.get_completion_progress() if hasattr(self, 'pg_tree_handler') else {},
            "task_status": {
                "total": len(self.task_queue),
                "completed": len(self.completed_tasks),
                "running": len(self.running_tasks),
                "failed": len(self.failed_tasks),
                "pending": len([t for t in self.task_queue if t.status == "pending"])
            },
            "stage_durations": self.stage_durations,
            "estimated_completion": self._estimate_completion_time()
        }

    def _estimate_completion_time(self) -> Optional[str]:
        """估算完成时间"""
        if not self.task_durations:
            return None

        pending_tasks = [t for t in self.task_queue if t.status == "pending"]
        if not pending_tasks:
            return None

        avg_duration = sum(self.task_durations.values()) / len(self.task_durations)
        estimated_seconds = len(pending_tasks) * avg_duration / self.max_concurrent_tasks

        estimated_completion = datetime.now() + timedelta(seconds=estimated_seconds)
        return estimated_completion.isoformat()