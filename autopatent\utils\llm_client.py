"""
LLM客户端 - 统一的大语言模型API调用接口
"""

import logging
from typing import Dict, Any, List, Optional, Union, AsyncGenerator
from enum import Enum
from datetime import datetime
import json
import asyncio
import aiohttp
import time
from dataclasses import dataclass
import openai
from openai import AsyncOpenAI
import httpx

from .token_tracker import TokenTracker, OperationType


class ModelProvider(Enum):
    """模型提供商枚举"""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    ANTHROPIC = "anthropic"
    QWEN = "qwen"
    BAICHUAN = "baichuan"
    ZHIPU = "zhipu"


class ResponseFormat(Enum):
    """响应格式枚举"""
    TEXT = "text"
    JSON = "json"
    STRUCTURED = "structured"


@dataclass
class ModelConfig:
    """模型配置数据类"""
    provider: ModelProvider
    model_name: str
    api_key: str
    base_url: str = ""
    max_tokens: int = 4096
    temperature: float = 0.7
    top_p: float = 0.9
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    timeout: int = 120
    max_retries: int = 3
    retry_delay: float = 1.0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'provider': self.provider.value,
            'model_name': self.model_name,
            'base_url': self.base_url,
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
            'top_p': self.top_p,
            'frequency_penalty': self.frequency_penalty,
            'presence_penalty': self.presence_penalty,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay
        }


@dataclass
class Message:
    """消息数据类"""
    role: str  # system, user, assistant
    content: str
    name: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            'role': self.role,
            'content': self.content
        }
        if self.name:
            result['name'] = self.name
        return result


@dataclass
class LLMResponse:
    """LLM响应数据类"""
    content: str
    model: str
    provider: ModelProvider
    usage: Dict[str, int]
    response_time: float
    finish_reason: str = "stop"
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'content': self.content,
            'model': self.model,
            'provider': self.provider.value,
            'usage': self.usage,
            'response_time': self.response_time,
            'finish_reason': self.finish_reason,
            'metadata': self.metadata
        }


class LLMClient:
    """LLM客户端 - 统一的大语言模型调用接口"""

    def __init__(self,
                 config: ModelConfig,
                 token_tracker: TokenTracker = None,
                 enable_logging: bool = True):
        """
        初始化LLM客户端

        Args:
            config: 模型配置
            token_tracker: Token追踪器
            enable_logging: 是否启用日志
        """
        self.config = config
        self.token_tracker = token_tracker or TokenTracker()
        self.enable_logging = enable_logging

        # 初始化客户端
        self._client = None
        self._async_client = None
        self._setup_client()

        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_tokens': 0,
            'total_cost': 0.0,
            'average_response_time': 0.0,
            'retry_count': 0
        }

        # 速率限制
        self.rate_limit = {
            'requests_per_minute': 60,
            'tokens_per_minute': 60000,
            'last_request_time': 0,
            'current_minute_requests': 0,
            'current_minute_tokens': 0,
            'minute_start': time.time()
        }

        # 日志记录器
        if enable_logging:
            self.logger = logging.getLogger(f"AutoPatent.LLMClient.{config.provider.value}")
        else:
            self.logger = logging.getLogger("dummy")
            self.logger.disabled = True

        self.logger.info(f"LLMClient初始化完成 - 提供商: {config.provider.value}, 模型: {config.model_name}")

    def _setup_client(self):
        """设置API客户端"""
        try:
            if self.config.provider == ModelProvider.OPENAI:
                self._client = openai.OpenAI(
                    api_key=self.config.api_key,
                    base_url=self.config.base_url or None,
                    timeout=self.config.timeout
                )
                self._async_client = AsyncOpenAI(
                    api_key=self.config.api_key,
                    base_url=self.config.base_url or None,
                    timeout=self.config.timeout
                )

            elif self.config.provider == ModelProvider.DEEPSEEK:
                self._client = openai.OpenAI(
                    api_key=self.config.api_key,
                    base_url=self.config.base_url or "https://api.deepseek.com",
                    timeout=self.config.timeout
                )
                self._async_client = AsyncOpenAI(
                    api_key=self.config.api_key,
                    base_url=self.config.base_url or "https://api.deepseek.com",
                    timeout=self.config.timeout
                )

            else:
                # 其他提供商的通用HTTP客户端
                self._setup_generic_client()

        except Exception as e:
            self.logger.error(f"设置API客户端失败: {e}")
            raise

    def _setup_generic_client(self):
        """设置通用HTTP客户端"""
        # 为其他提供商设置通用的HTTP客户端
        self._http_client = httpx.AsyncClient(
            timeout=self.config.timeout,
            headers={
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
        )

    def generate(self,
                 messages: List[Message],
                 temperature: float = None,
                 max_tokens: int = None,
                 response_format: ResponseFormat = ResponseFormat.TEXT,
                 **kwargs) -> LLMResponse:
        """
        同步生成响应

        Args:
            messages: 消息列表
            temperature: 生成温度
            max_tokens: 最大token数
            response_format: 响应格式
            **kwargs: 其他参数

        Returns:
            LLM响应对象
        """
        start_time = time.time()

        try:
            # 检查速率限制
            self._check_rate_limit()

            # 准备请求参数
            request_params = self._prepare_request_params(
                messages, temperature, max_tokens, response_format, **kwargs
            )

            # 调用API
            response = self._make_request(request_params)

            # 处理响应
            llm_response = self._process_response(response, start_time)

            # 更新统计
            self._update_stats(llm_response, True)

            # 记录token使用
            self._record_token_usage(llm_response)

            return llm_response

        except Exception as e:
            response_time = time.time() - start_time
            self.logger.error(f"生成响应失败: {e}")

            # 更新失败统计
            self._update_stats(None, False, response_time)

            raise

    async def generate_async(self,
                             messages: List[Message],
                             temperature: float = None,
                             max_tokens: int = None,
                             response_format: ResponseFormat = ResponseFormat.TEXT,
                             **kwargs) -> LLMResponse:
        """
        异步生成响应

        Args:
            messages: 消息列表
            temperature: 生成温度
            max_tokens: 最大token数
            response_format: 响应格式
            **kwargs: 其他参数

        Returns:
            LLM响应对象
        """
        start_time = time.time()

        try:
            # 检查速率限制
            await self._check_rate_limit_async()

            # 准备请求参数
            request_params = self._prepare_request_params(
                messages, temperature, max_tokens, response_format, **kwargs
            )

            # 异步调用API
            response = await self._make_request_async(request_params)

            # 处理响应
            llm_response = self._process_response(response, start_time)

            # 更新统计
            self._update_stats(llm_response, True)

            # 记录token使用
            self._record_token_usage(llm_response)

            return llm_response

        except Exception as e:
            response_time = time.time() - start_time
            self.logger.error(f"异步生成响应失败: {e}")

            # 更新失败统计
            self._update_stats(None, False, response_time)

            raise

    async def generate_stream(self,
                              messages: List[Message],
                              temperature: float = None,
                              max_tokens: int = None,
                              **kwargs) -> AsyncGenerator[str, None]:
        """
        流式生成响应

        Args:
            messages: 消息列表
            temperature: 生成温度
            max_tokens: 最大token数
            **kwargs: 其他参数

        Yields:
            生成的文本片段
        """
        try:
            # 检查速率限制
            await self._check_rate_limit_async()

            # 准备流式请求参数
            request_params = self._prepare_request_params(
                messages, temperature, max_tokens, ResponseFormat.TEXT, stream=True, **kwargs
            )

            # 异步流式调用
            async for chunk in self._make_stream_request_async(request_params):
                yield chunk

        except Exception as e:
            self.logger.error(f"流式生成失败: {e}")
            raise

    def _prepare_request_params(self,
                                messages: List[Message],
                                temperature: float = None,
                                max_tokens: int = None,
                                response_format: ResponseFormat = ResponseFormat.TEXT,
                                **kwargs) -> Dict[str, Any]:
        """准备请求参数"""
        params = {
            'model': self.config.model_name,
            'messages': [msg.to_dict() for msg in messages],
            'temperature': temperature if temperature is not None else self.config.temperature,
            'max_tokens': max_tokens if max_tokens is not None else self.config.max_tokens,
            'top_p': kwargs.get('top_p', self.config.top_p),
            'frequency_penalty': kwargs.get('frequency_penalty', self.config.frequency_penalty),
            'presence_penalty': kwargs.get('presence_penalty', self.config.presence_penalty)
        }

        # 处理响应格式
        if response_format == ResponseFormat.JSON:
            if self.config.provider in [ModelProvider.OPENAI, ModelProvider.DEEPSEEK]:
                params['response_format'] = {'type': 'json_object'}

        # 添加其他参数
        for key, value in kwargs.items():
            if key not in params:
                params[key] = value

        return params

    def _make_request(self, params: Dict[str, Any]) -> Any:
        """发起API请求"""
        retries = 0
        last_exception = None

        while retries <= self.config.max_retries:
            try:
                if self.config.provider in [ModelProvider.OPENAI, ModelProvider.DEEPSEEK]:
                    return self._client.chat.completions.create(**params)
                else:
                    return self._make_generic_request(params)

            except Exception as e:
                last_exception = e
                retries += 1
                self.stats['retry_count'] += 1

                if retries <= self.config.max_retries:
                    self.logger.warning(f"API请求失败，重试第 {retries} 次: {e}")
                    time.sleep(self.config.retry_delay * retries)
                else:
                    self.logger.error(f"API请求最终失败: {e}")
                    raise e

        raise last_exception

    async def _make_request_async(self, params: Dict[str, Any]) -> Any:
        """异步发起API请求"""
        retries = 0
        last_exception = None

        while retries <= self.config.max_retries:
            try:
                if self.config.provider in [ModelProvider.OPENAI, ModelProvider.DEEPSEEK]:
                    return await self._async_client.chat.completions.create(**params)
                else:
                    return await self._make_generic_request_async(params)

            except Exception as e:
                last_exception = e
                retries += 1
                self.stats['retry_count'] += 1

                if retries <= self.config.max_retries:
                    self.logger.warning(f"异步API请求失败，重试第 {retries} 次: {e}")
                    await asyncio.sleep(self.config.retry_delay * retries)
                else:
                    self.logger.error(f"异步API请求最终失败: {e}")
                    raise e

        raise last_exception

    async def _make_stream_request_async(self, params: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """异步流式请求"""
        try:
            if self.config.provider in [ModelProvider.OPENAI, ModelProvider.DEEPSEEK]:
                stream = await self._async_client.chat.completions.create(**params)

                async for chunk in stream:
                    if chunk.choices and chunk.choices[0].delta.content:
                        yield chunk.choices[0].delta.content
            else:
                # 其他提供商的流式实现
                async for chunk in self._make_generic_stream_request_async(params):
                    yield chunk

        except Exception as e:
            self.logger.error(f"流式请求失败: {e}")
            raise

    def _make_generic_request(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """通用HTTP请求"""
        # 为其他提供商实现通用的HTTP请求
        # 这里是示例实现，实际需要根据具体API规范调整

        import requests

        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }

        response = requests.post(
            self.config.base_url + "/v1/chat/completions",
            headers=headers,
            json=params,
            timeout=self.config.timeout
        )

        response.raise_for_status()
        return response.json()

    async def _make_generic_request_async(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """异步通用HTTP请求"""
        async with aiohttp.ClientSession() as session:
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }

            async with session.post(
                    self.config.base_url + "/v1/chat/completions",
                    headers=headers,
                    json=params,
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            ) as response:
                response.raise_for_status()
                return await response.json()

    async def _make_generic_stream_request_async(self, params: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """异步通用流式请求"""
        async with aiohttp.ClientSession() as session:
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }

            async with session.post(
                    self.config.base_url + "/v1/chat/completions",
                    headers=headers,
                    json=params,
                    timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            ) as response:
                response.raise_for_status()

                async for line in response.content:
                    if line:
                        try:
                            # 解析SSE格式
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]
                                if data_str != '[DONE]':
                                    data = json.loads(data_str)
                                    if 'choices' in data and data['choices']:
                                        content = data['choices'][0].get('delta', {}).get('content', '')
                                        if content:
                                            yield content
                        except Exception as e:
                            self.logger.warning(f"解析流式响应失败: {e}")
                            continue

    def _process_response(self, response: Any, start_time: float) -> LLMResponse:
        """处理API响应"""
        response_time = time.time() - start_time

        if self.config.provider in [ModelProvider.OPENAI, ModelProvider.DEEPSEEK]:
            # OpenAI格式响应
            choice = response.choices[0]
            content = choice.message.content
            finish_reason = choice.finish_reason

            usage = {
                'prompt_tokens': response.usage.prompt_tokens,
                'completion_tokens': response.usage.completion_tokens,
                'total_tokens': response.usage.total_tokens
            }

            return LLMResponse(
                content=content,
                model=response.model,
                provider=self.config.provider,
                usage=usage,
                response_time=response_time,
                finish_reason=finish_reason,
                metadata={'response_id': response.id}
            )

        else:
            # 通用格式响应
            return self._process_generic_response(response, response_time)

    def _process_generic_response(self, response: Dict[str, Any], response_time: float) -> LLMResponse:
        """处理通用格式响应"""
        choice = response.get('choices', [{}])[0]
        message = choice.get('message', {})
        content = message.get('content', '')
        finish_reason = choice.get('finish_reason', 'stop')

        usage = response.get('usage', {
            'prompt_tokens': 0,
            'completion_tokens': 0,
            'total_tokens': 0
        })

        return LLMResponse(
            content=content,
            model=response.get('model', self.config.model_name),
            provider=self.config.provider,
            usage=usage,
            response_time=response_time,
            finish_reason=finish_reason,
            metadata={'response_id': response.get('id', '')}
        )

    def _check_rate_limit(self):
        """检查速率限制"""
        current_time = time.time()

        # 重置分钟计数器
        if current_time - self.rate_limit['minute_start'] >= 60:
            self.rate_limit['minute_start'] = current_time
            self.rate_limit['current_minute_requests'] = 0
            self.rate_limit['current_minute_tokens'] = 0

        # 检查请求速率
        if self.rate_limit['current_minute_requests'] >= self.rate_limit['requests_per_minute']:
            wait_time = 60 - (current_time - self.rate_limit['minute_start'])
            if wait_time > 0:
                self.logger.warning(f"达到请求速率限制，等待 {wait_time:.2f} 秒")
                time.sleep(wait_time)
                self._check_rate_limit()  # 递归检查
                return

        # 更新计数器
        self.rate_limit['current_minute_requests'] += 1
        self.rate_limit['last_request_time'] = current_time

    async def _check_rate_limit_async(self):
        """异步检查速率限制"""
        current_time = time.time()

        # 重置分钟计数器
        if current_time - self.rate_limit['minute_start'] >= 60:
            self.rate_limit['minute_start'] = current_time
            self.rate_limit['current_minute_requests'] = 0
            self.rate_limit['current_minute_tokens'] = 0

        # 检查请求速率
        if self.rate_limit['current_minute_requests'] >= self.rate_limit['requests_per_minute']:
            wait_time = 60 - (current_time - self.rate_limit['minute_start'])
            if wait_time > 0:
                self.logger.warning(f"达到请求速率限制，等待 {wait_time:.2f} 秒")
                await asyncio.sleep(wait_time)
                await self._check_rate_limit_async()  # 递归检查
                return

        # 更新计数器
        self.rate_limit['current_minute_requests'] += 1
        self.rate_limit['last_request_time'] = current_time

    def _record_token_usage(self, response: LLMResponse):
        """记录token使用情况"""
        if not self.token_tracker:
            return

        try:
            self.token_tracker.add_tokens(
                operation=OperationType.CONTENT_GENERATION,
                agent_name="LLMClient",
                input_tokens=response.usage['prompt_tokens'],
                output_tokens=response.usage['completion_tokens'],
                model_name=response.model,
                response_time=response.response_time,
                success=True,
                provider=response.provider.value
            )
        except Exception as e:
            self.logger.error(f"记录token使用失败: {e}")

    def _update_stats(self, response: LLMResponse = None, success: bool = True, response_time: float = 0):
        """更新统计信息"""
        self.stats['total_requests'] += 1

        if success and response:
            self.stats['successful_requests'] += 1
            self.stats['total_tokens'] += response.usage['total_tokens']

            # 更新平均响应时间
            total_requests = self.stats['total_requests']
            current_avg = self.stats['average_response_time']
            self.stats['average_response_time'] = (
                    (current_avg * (total_requests - 1) + response.response_time) / total_requests
            )

            # 估算成本
            cost = self._estimate_cost(response.usage)
            self.stats['total_cost'] += cost

        else:
            self.stats['failed_requests'] += 1
            if response_time > 0:
                total_requests = self.stats['total_requests']
                current_avg = self.stats['average_response_time']
                self.stats['average_response_time'] = (
                        (current_avg * (total_requests - 1) + response_time) / total_requests
                )

    def _estimate_cost(self, usage: Dict[str, int]) -> float:
        """估算API调用成本"""
        # 基于不同提供商的定价模型
        pricing = {
            ModelProvider.OPENAI: {
                'gpt-4': {'input': 0.03, 'output': 0.06},  # per 1K tokens
                'gpt-3.5-turbo': {'input': 0.0015, 'output': 0.002}
            },
            ModelProvider.DEEPSEEK: {
                'deepseek-chat': {'input': 0.00014, 'output': 0.00028},
                'deepseek-coder': {'input': 0.00014, 'output': 0.00028}
            }
        }

        provider_pricing = pricing.get(self.config.provider, {})
        model_pricing = provider_pricing.get(self.config.model_name, {'input': 0.001, 'output': 0.002})

        input_cost = (usage['prompt_tokens'] / 1000) * model_pricing['input']
        output_cost = (usage['completion_tokens'] / 1000) * model_pricing['output']

        return input_cost + output_cost

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()

        # 计算成功率
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
        else:
            stats['success_rate'] = 0.0

        # 添加配置信息
        stats['configuration'] = self.config.to_dict()
        stats['rate_limit'] = self.rate_limit.copy()

        return stats

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_tokens': 0,
            'total_cost': 0.0,
            'average_response_time': 0.0,
            'retry_count': 0
        }

        self.logger.info("LLMClient统计信息已重置")

    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                old_value = getattr(self.config, key)
                setattr(self.config, key, value)
                self.logger.info(f"配置更新: {key}: {old_value} -> {value}")

                # 如果更新了关键配置，重新设置客户端
                if key in ['api_key', 'base_url', 'provider']:
                    self._setup_client()

    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            test_messages = [Message(role="user", content="Hello")]
            response = self.generate(test_messages, max_tokens=10)

            self.logger.info("API连接测试成功")
            return True

        except Exception as e:
            self.logger.error(f"API连接测试失败: {e}")
            return False

    async def test_connection_async(self) -> bool:
        """异步测试API连接"""
        try:
            test_messages = [Message(role="user", content="Hello")]
            response = await self.generate_async(test_messages, max_tokens=10)

            self.logger.info("异步API连接测试成功")
            return True

        except Exception as e:
            self.logger.error(f"异步API连接测试失败: {e}")
            return False

    def __repr__(self) -> str:
        """字符串表示"""
        return (f"LLMClient(provider={self.config.provider.value}, "
                f"model={self.config.model_name})")


class LLMClientManager:
    """LLM客户端管理器 - 管理多个LLM客户端"""

    def __init__(self, token_tracker: TokenTracker = None):
        """
        初始化LLM客户端管理器

        Args:
            token_tracker: 共享的Token追踪器
        """
        self.token_tracker = token_tracker or TokenTracker()
        self.clients: Dict[str, LLMClient] = {}
        self.default_client_name = None

        self.logger = logging.getLogger("AutoPatent.LLMClientManager")
        self.logger.info("LLMClientManager初始化完成")

    def add_client(self, name: str, config: ModelConfig, set_as_default: bool = False) -> bool:
        """
        添加LLM客户端

        Args:
            name: 客户端名称
            config: 模型配置
            set_as_default: 是否设为默认客户端

        Returns:
            是否添加成功
        """
        try:
            client = LLMClient(config, self.token_tracker)
            self.clients[name] = client

            if set_as_default or not self.default_client_name:
                self.default_client_name = name

            self.logger.info(f"LLM客户端已添加: {name} ({config.provider.value})")
            return True

        except Exception as e:
            self.logger.error(f"添加LLM客户端失败: {e}")
            return False

    def get_client(self, name: str = None) -> Optional[LLMClient]:
        """
        获取LLM客户端

        Args:
            name: 客户端名称，None表示获取默认客户端

        Returns:
            LLM客户端对象
        """
        if name is None:
            name = self.default_client_name

        return self.clients.get(name)

    def remove_client(self, name: str) -> bool:
        """
        移除LLM客户端

        Args:
            name: 客户端名称

        Returns:
            是否移除成功
        """
        if name in self.clients:
            del self.clients[name]

            if name == self.default_client_name:
                self.default_client_name = next(iter(self.clients.keys())) if self.clients else None

            self.logger.info(f"LLM客户端已移除: {name}")
            return True

        return False

    def list_clients(self) -> List[str]:
        """获取所有客户端名称列表"""
        return list(self.clients.keys())

    def get_aggregated_stats(self) -> Dict[str, Any]:
        """获取所有客户端的聚合统计"""
        total_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_tokens': 0,
            'total_cost': 0.0,
            'clients_count': len(self.clients),
            'client_stats': {}
        }

        for name, client in self.clients.items():
            client_stats = client.get_stats()
            total_stats['client_stats'][name] = client_stats

            total_stats['total_requests'] += client_stats['total_requests']
            total_stats['successful_requests'] += client_stats['successful_requests']
            total_stats['failed_requests'] += client_stats['failed_requests']
            total_stats['total_tokens'] += client_stats['total_tokens']
            total_stats['total_cost'] += client_stats['total_cost']

        # 计算总体成功率
        if total_stats['total_requests'] > 0:
            total_stats['success_rate'] = total_stats['successful_requests'] / total_stats['total_requests']
        else:
            total_stats['success_rate'] = 0.0

        return total_stats