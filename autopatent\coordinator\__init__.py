"""
协调器模块 - Coordination and Workflow Management

负责多智能体协调、工作流管理和任务调度，
实现高效的专利生成pipeline。
"""

from .coordinator import (
    Coordinator,
    CoordinationStrategy,
    WorkflowPhase,
    AgentTaskResult
)

from .workflow_manager import (
    WorkflowManager,
    WorkflowStatus,
    ExecutionMode,
    TaskNode,
    WorkflowMetrics
)

# 便捷函数
def create_coordinator(agents: dict,
                      strategy: CoordinationStrategy = CoordinationStrategy.ADAPTIVE,
                      **kwargs) -> Coordinator:
    """
    创建协调器实例

    Args:
        agents: 智能体字典 {'planner': agent, 'writer': agent, 'examiner': agent}
        strategy: 协调策略
        **kwargs: 其他配置参数

    Returns:
        配置好的协调器
    """
    return Coordinator(
        agents=agents,
        coordination_strategy=strategy,
        **kwargs
    )

def create_workflow_manager(max_concurrent_tasks: int = 3,
                          execution_mode: ExecutionMode = ExecutionMode.ADAPTIVE,
                          **kwargs) -> WorkflowManager:
    """
    创建工作流管理器

    Args:
        max_concurrent_tasks: 最大并发任务数
        execution_mode: 执行模式
        **kwargs: 其他配置参数

    Returns:
        配置好的工作流管理器
    """
    return WorkflowManager(
        max_concurrent_tasks=max_concurrent_tasks,
        execution_mode=execution_mode,
        **kwargs
    )

def setup_patent_generation_pipeline(agents: dict,
                                    coordination_strategy: CoordinationStrategy = CoordinationStrategy.ADAPTIVE,
                                    execution_mode: ExecutionMode = ExecutionMode.ADAPTIVE,
                                    max_concurrent_tasks: int = 3) -> tuple:
    """
    设置完整的专利生成pipeline

    Args:
        agents: 智能体字典
        coordination_strategy: 协调策略
        execution_mode: 执行模式
        max_concurrent_tasks: 最大并发任务数

    Returns:
        (coordinator, workflow_manager) 元组
    """
    coordinator = create_coordinator(
        agents=agents,
        strategy=coordination_strategy
    )

    workflow_manager = create_workflow_manager(
        max_concurrent_tasks=max_concurrent_tasks,
        execution_mode=execution_mode
    )

    return coordinator, workflow_manager

# 导出的公共接口
__all__ = [
    # 协调器
    "Coordinator",
    "CoordinationStrategy",
    "WorkflowPhase",
    "AgentTaskResult",

    # 工作流管理器
    "WorkflowManager",
    "WorkflowStatus",
    "ExecutionMode",
    "TaskNode",
    "WorkflowMetrics",

    # 便捷函数
    "create_coordinator",
    "create_workflow_manager",
    "setup_patent_generation_pipeline",
]