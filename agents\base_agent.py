"""
基础智能体类 - 所有智能体的抽象基类
"""

import logging
from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod
from datetime import datetime
import json
import asyncio
from enum import Enum

from utils import TokenTracker, OperationType


class AgentStatus(Enum):
    """智能体状态枚举"""
    IDLE = "idle"  # 空闲
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    ERROR = "error"  # 错误状态
    PAUSED = "paused"  # 暂停


class AgentCapability(Enum):
    """智能体能力枚举"""
    PLANNING = "planning"  # 规划能力
    WRITING = "writing"  # 写作能力
    EXAMINING = "examining"  # 审查能力
    REASONING = "reasoning"  # 推理能力
    RETRIEVAL = "retrieval"  # 检索能力
    OPTIMIZATION = "optimization"  # 优化能力


class BaseAgent(ABC):
    """
    基础智能体抽象类

    定义了所有智能体共同的接口和基础功能
    """

    def __init__(self,
                 agent_name: str,
                 model_name: str = "deepseek-chat",
                 temperature: float = 0.7,
                 max_tokens: int = 2000,
                 capabilities: List[AgentCapability] = None,
                 enable_logging: bool = True,
                 **kwargs):
        """
        初始化基础智能体

        Args:
            agent_name: 智能体名称
            model_name: 使用的LLM模型名称
            temperature: 生成温度参数
            max_tokens: 最大token数量
            capabilities: 智能体能力列表
            enable_logging: 是否启用日志
            **kwargs: 其他配置参数
        """
        self.agent_name = agent_name
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.capabilities = capabilities or []
        self.enable_logging = enable_logging

        # 状态管理
        self.status = AgentStatus.IDLE
        self.created_at = datetime.now()
        self.last_active_at = datetime.now()

        # 配置参数
        self.config = {
            'model_name': model_name,
            'temperature': temperature,
            'max_tokens': max_tokens,
            'retry_count': kwargs.get('retry_count', 3),
            'timeout_seconds': kwargs.get('timeout_seconds', 120),
            'enable_cache': kwargs.get('enable_cache', True)
        }

        # Token追踪器
        self.token_tracker = TokenTracker()

        # 性能统计
        self.performance_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'total_input_tokens': 0,
            'total_output_tokens': 0,
            'total_cost': 0.0
        }

        # 错误记录
        self.error_history = []
        self.max_error_history = 100

        # 日志记录器
        if enable_logging:
            self.logger = logging.getLogger(f"AutoPatent.{agent_name}")
        else:
            self.logger = logging.getLogger("dummy")
            self.logger.disabled = True

        self.logger.info(f"{agent_name} 初始化完成")

    @abstractmethod
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入数据的抽象方法

        Args:
            input_data: 输入数据字典

        Returns:
            处理结果字典
        """
        pass

    def _generate_response(self,
                           prompt: str,
                           system_prompt: str = None,
                           temperature: float = None,
                           max_tokens: int = None,
                           **kwargs) -> str:
        """
        生成LLM响应的核心方法

        Args:
            prompt: 用户prompt
            system_prompt: 系统prompt
            temperature: 生成温度
            max_tokens: 最大token数
            **kwargs: 其他参数

        Returns:
            生成的响应文本
        """
        start_time = datetime.now()

        # 更新状态
        self.status = AgentStatus.PROCESSING
        self.last_active_at = start_time

        try:
            # 使用传入参数或默认配置
            actual_temperature = temperature if temperature is not None else self.temperature
            actual_max_tokens = max_tokens if max_tokens is not None else self.max_tokens

            # 构建请求参数
            request_params = {
                'model': self.model_name,
                'temperature': actual_temperature,
                'max_tokens': actual_max_tokens,
                'messages': []
            }

            # 添加系统prompt
            if system_prompt:
                request_params['messages'].append({
                    'role': 'system',
                    'content': system_prompt
                })

            # 添加用户prompt
            request_params['messages'].append({
                'role': 'user',
                'content': prompt
            })

            # 调用LLM API（这里需要实际的API调用实现）
            response = self._call_llm_api(request_params)

            # 提取响应内容
            response_content = self._extract_response_content(response)

            # 记录token使用
            input_tokens = self._estimate_tokens(prompt + (system_prompt or ''))
            output_tokens = self._estimate_tokens(response_content)

            self._record_token_usage(input_tokens, output_tokens, start_time)

            # 更新状态
            self.status = AgentStatus.COMPLETED
            self.performance_stats['successful_requests'] += 1

            return response_content

        except Exception as e:
            self.status = AgentStatus.ERROR
            self.performance_stats['failed_requests'] += 1
            self._record_error(e, prompt, system_prompt)

            self.logger.error(f"生成响应失败: {e}")
            raise

        finally:
            # 更新性能统计
            self.performance_stats['total_requests'] += 1
            response_time = (datetime.now() - start_time).total_seconds()
            self._update_response_time_stats(response_time)

    def _call_llm_api(self, request_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用LLM API的方法（需要在子类中实现具体的API调用）

        Args:
            request_params: API请求参数

        Returns:
            API响应结果
        """
        # 这里是模拟实现，实际使用中需要调用真实的LLM API
        self.logger.debug(f"调用 {self.model_name} API")

        # 模拟API调用延迟
        import time
        time.sleep(0.1)

        # 模拟响应
        mock_response = {
            'choices': [{
                'message': {
                    'content': f"模拟来自{self.model_name}的响应内容"
                }
            }],
            'usage': {
                'prompt_tokens': 100,
                'completion_tokens': 50,
                'total_tokens': 150
            }
        }

        return mock_response

    def _extract_response_content(self, response: Dict[str, Any]) -> str:
        """从API响应中提取内容"""
        try:
            return response['choices'][0]['message']['content']
        except (KeyError, IndexError) as e:
            self.logger.error(f"提取响应内容失败: {e}")
            return ""

    def _estimate_tokens(self, text: str) -> int:
        """估算文本的token数量"""
        # 简化的token估算：中文字符约1个token，英文单词约0.75个token
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        english_words = len([w for w in text.split() if w.isalpha()])
        other_chars = len(text) - chinese_chars

        estimated_tokens = chinese_chars + int(english_words * 0.75) + int(other_chars * 0.5)
        return max(1, estimated_tokens)

    def _record_token_usage(self, input_tokens: int, output_tokens: int, start_time: datetime):
        """记录token使用情况"""
        response_time = (datetime.now() - start_time).total_seconds()

        # 确定操作类型
        operation_type = self._get_operation_type()

        # 记录到token tracker
        self.token_tracker.add_tokens(
            operation=operation_type,
            agent_name=self.agent_name,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            model_name=self.model_name,
            response_time=response_time,
            success=True
        )

        # 更新本地统计
        self.performance_stats['total_input_tokens'] += input_tokens
        self.performance_stats['total_output_tokens'] += output_tokens

        # 估算成本（需要根据实际模型定价）
        estimated_cost = self._estimate_cost(input_tokens, output_tokens)
        self.performance_stats['total_cost'] += estimated_cost

    def _get_operation_type(self) -> OperationType:
        """根据智能体类型确定操作类型"""
        if AgentCapability.PLANNING in self.capabilities:
            return OperationType.PLANNING
        elif AgentCapability.WRITING in self.capabilities:
            return OperationType.WRITING
        elif AgentCapability.EXAMINING in self.capabilities:
            return OperationType.EXAMINING
        else:
            return OperationType.CONTENT_GENERATION

    def _estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """估算API调用成本"""
        # 基于DeepSeek定价的简化估算
        input_cost = (input_tokens / 1000) * 0.00014  # $0.14 per 1M tokens
        output_cost = (output_tokens / 1000) * 0.00028  # $0.28 per 1M tokens
        return input_cost + output_cost

    def _record_error(self, error: Exception, prompt: str, system_prompt: str = None):
        """记录错误信息"""
        error_record = {
            'timestamp': datetime.now().isoformat(),
            'error_type': type(error).__name__,
            'error_message': str(error),
            'prompt_length': len(prompt),
            'system_prompt_length': len(system_prompt) if system_prompt else 0,
            'agent_status': self.status.value
        }

        self.error_history.append(error_record)

        # 保持错误历史记录在限制范围内
        if len(self.error_history) > self.max_error_history:
            self.error_history.pop(0)

    def _update_response_time_stats(self, response_time: float):
        """更新响应时间统计"""
        total_requests = self.performance_stats['total_requests']
        if total_requests == 1:
            self.performance_stats['average_response_time'] = response_time
        else:
            current_avg = self.performance_stats['average_response_time']
            new_avg = ((current_avg * (total_requests - 1)) + response_time) / total_requests
            self.performance_stats['average_response_time'] = new_avg

    async def process_async(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        异步处理方法

        Args:
            input_data: 输入数据

        Returns:
            处理结果
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.process, input_data)

    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """
        验证输入数据

        Args:
            input_data: 输入数据

        Returns:
            是否有效
        """
        if not isinstance(input_data, dict):
            self.logger.error("输入数据必须是字典类型")
            return False

        return True

    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态信息"""
        return {
            'agent_name': self.agent_name,
            'status': self.status.value,
            'capabilities': [cap.value for cap in self.capabilities],
            'created_at': self.created_at.isoformat(),
            'last_active_at': self.last_active_at.isoformat(),
            'model_name': self.model_name,
            'config': self.config.copy()
        }

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.performance_stats.copy()

        # 计算成功率
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
        else:
            stats['success_rate'] = 0.0

        # 添加token效率指标
        if stats['total_input_tokens'] > 0:
            stats['tokens_per_request'] = (stats['total_input_tokens'] + stats['total_output_tokens']) / stats[
                'total_requests']
        else:
            stats['tokens_per_request'] = 0.0

        return stats

    def get_token_usage(self) -> Dict[str, Any]:
        """获取token使用统计"""
        return self.token_tracker.get_usage()

    def get_error_history(self) -> List[Dict[str, Any]]:
        """获取错误历史记录"""
        return self.error_history.copy()

    def reset_stats(self):
        """重置性能统计"""
        self.performance_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'total_input_tokens': 0,
            'total_output_tokens': 0,
            'total_cost': 0.0
        }
        self.error_history.clear()
        self.token_tracker.reset_session()

        self.logger.info(f"{self.agent_name} 统计信息已重置")

    def update_config(self, **kwargs):
        """更新配置参数"""
        for key, value in kwargs.items():
            if key in self.config:
                old_value = self.config[key]
                self.config[key] = value
                self.logger.info(f"配置更新: {key}: {old_value} -> {value}")

                # 同步更新对应的实例属性
                if key == 'temperature':
                    self.temperature = value
                elif key == 'max_tokens':
                    self.max_tokens = value
                elif key == 'model_name':
                    self.model_name = value

    def has_capability(self, capability: AgentCapability) -> bool:
        """检查是否具有特定能力"""
        return capability in self.capabilities

    def add_capability(self, capability: AgentCapability):
        """添加能力"""
        if capability not in self.capabilities:
            self.capabilities.append(capability)
            self.logger.info(f"添加能力: {capability.value}")

    def remove_capability(self, capability: AgentCapability):
        """移除能力"""
        if capability in self.capabilities:
            self.capabilities.remove(capability)
            self.logger.info(f"移除能力: {capability.value}")

    def pause(self):
        """暂停智能体"""
        self.status = AgentStatus.PAUSED
        self.logger.info(f"{self.agent_name} 已暂停")

    def resume(self):
        """恢复智能体"""
        self.status = AgentStatus.IDLE
        self.logger.info(f"{self.agent_name} 已恢复")

    def is_healthy(self) -> bool:
        """检查智能体健康状态"""
        # 检查错误率
        if self.performance_stats['total_requests'] > 10:
            error_rate = self.performance_stats['failed_requests'] / self.performance_stats['total_requests']
            if error_rate > 0.5:  # 错误率超过50%
                return False

        # 检查状态
        if self.status == AgentStatus.ERROR:
            return False

        # 检查最近活动时间
        inactive_time = (datetime.now() - self.last_active_at).total_seconds()
        if inactive_time > 3600:  # 超过1小时未活动
            return False

        return True

    def get_health_report(self) -> Dict[str, Any]:
        """获取健康状况报告"""
        stats = self.get_performance_stats()

        return {
            'is_healthy': self.is_healthy(),
            'status': self.status.value,
            'error_rate': stats.get('success_rate', 0.0),
            'last_active': self.last_active_at.isoformat(),
            'total_requests': stats['total_requests'],
            'recent_errors': self.error_history[-5:] if self.error_history else [],
            'performance_indicators': {
                'avg_response_time': stats['average_response_time'],
                'tokens_per_request': stats.get('tokens_per_request', 0.0),
                'total_cost': stats['total_cost']
            }
        }

    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.agent_name}(status={self.status.value}, model={self.model_name})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"{self.__class__.__name__}("
                f"agent_name='{self.agent_name}', "
                f"status={self.status.value}, "
                f"model='{self.model_name}', "
                f"capabilities={[c.value for c in self.capabilities]})")