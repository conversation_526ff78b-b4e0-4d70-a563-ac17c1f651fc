{"导出信息": {"导出时间": "2025-06-09T18:44:13.693915", "session_id": "patent_gen_001", "记录数量": 2, "格式版本": "1.0"}, "session统计": {"session_id": "patent_gen_001", "start_time": "2025-06-09 18:44:11.870247", "total_input_tokens": 1500, "total_output_tokens": 800, "total_reasoning_tokens": 0, "total_cost": 0.000434, "api_calls": 1, "successful_calls": 1, "failed_calls": 0, "operations": {"planning": 1}, "models_used": {"deepseek-chat": 1}, "agents_used": {"PlannerAgent": 1}, "duration_seconds": 1.823668, "duration_formatted": "0:00:01", "total_tokens": 2300, "average_response_time": 2.5, "success_rate": 100.0}, "详细分析": {"总记录数": 2, "时间范围": {"开始时间": "2025-06-09T18:44:11.870247", "结束时间": "2025-06-09T18:44:11.870247"}, "token统计": {"input": 1500, "output": 800}, "成本统计": {"input": 0.00021, "output": 0.000224}, "总成本": 0.000434, "操作统计": {"planning": {"次数": 2, "tokens": 2300, "成本": 0.000434}}, "智能体统计": {"PlannerAgent": {"次数": 2, "tokens": 2300, "成本": 0.000434}}, "模型统计": {"deepseek-chat": {"次数": 2, "tokens": 2300, "成本": 0.000434}}, "性能统计": {"成功率": 100.0, "平均响应时间": 2.5, "最快响应": 2.5, "最慢响应": 2.5, "失败次数": 0}, "时间趋势": {"2025-06-09 18:00": {"tokens": 2300, "成本": 0.00043400000000000003, "调用次数": 2}}}, "成本估算": {"input_cost": 0.00021, "output_cost": 0.000224, "reasoning_cost": 0.0, "total_cost": 0.000434, "input_rate": 0.00014, "output_rate": 0.00028, "currency": "USD"}, "原始记录": [{"timestamp": "2025-06-09T18:44:11.870247", "operation": "planning", "agent_name": "PlannerAgent", "token_type": "input", "count": 1500, "model_name": "deepseek-chat", "cost_estimate": 0.00021, "response_time": 2.5, "session_id": "patent_gen_001", "workflow_id": "", "node_id": "", "input_length": 0, "output_length": 0, "complexity_score": 1.0, "success": true, "error_message": ""}, {"timestamp": "2025-06-09T18:44:11.870247", "operation": "planning", "agent_name": "PlannerAgent", "token_type": "output", "count": 800, "model_name": "deepseek-chat", "cost_estimate": 0.000224, "response_time": 2.5, "session_id": "patent_gen_001", "workflow_id": "", "node_id": "", "input_length": 0, "output_length": 0, "complexity_score": 1.0, "success": true, "error_message": ""}]}