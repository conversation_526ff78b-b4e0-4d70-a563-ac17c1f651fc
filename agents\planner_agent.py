"""
规划智能体 - 负责专利生成的整体规划和PGTree结构设计
"""

import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from enum import Enum
from datetime import datetime
import json
import re
from dataclasses import dataclass, field

from .base_agent import BaseAgent, AgentCapability
from ..utils.pg_tree_handler import PGTreeHandler, PGTreeNode, NodeStatus
from ..utils.token_tracker import OperationType


class PlanningStrategy(Enum):
    """规划策略枚举"""
    STANDARD = "standard"  # 标准规划
    DETAILED = "detailed"  # 详细规划
    SIMPLIFIED = "simplified"  # 简化规划
    ADAPTIVE = "adaptive"  # 自适应规划
    CUSTOM = "custom"  # 自定义规划


class PatentType(Enum):
    """专利类型枚举"""
    INVENTION = "invention"  # 发明专利
    UTILITY_MODEL = "utility_model"  # 实用新型
    DESIGN = "design"  # 外观设计
    SOFTWARE = "software"  # 软件专利
    METHOD = "method"  # 方法专利


class PriorityLevel(Enum):
    """优先级枚举"""
    CRITICAL = "critical"  # 关键
    HIGH = "high"  # 高
    MEDIUM = "medium"  # 中
    LOW = "low"  # 低


@dataclass
class SectionPlan:
    """Section规划数据类"""
    section_id: str
    section_name: str
    priority: PriorityLevel
    estimated_words: int
    dependencies: List[str] = field(default_factory=list)
    requirements: str = ""
    writing_guide: str = ""
    complexity_level: float = 1.0
    estimated_time_minutes: int = 30

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'section_id': self.section_id,
            'section_name': self.section_name,
            'priority': self.priority.value,
            'estimated_words': self.estimated_words,
            'dependencies': self.dependencies,
            'requirements': self.requirements,
            'writing_guide': self.writing_guide,
            'complexity_level': self.complexity_level,
            'estimated_time_minutes': self.estimated_time_minutes
        }


@dataclass
class PGTree:
    """PGTree规划结果数据类"""
    tree_id: str
    concept: str
    patent_type: PatentType
    strategy: PlanningStrategy
    structure: Dict[str, SectionPlan]
    total_estimated_words: int
    total_estimated_time: int
    creation_timestamp: str

    def get_dependency_order(self) -> List[str]:
        """获取基于依赖关系的执行顺序"""
        # 拓扑排序获得依赖顺序
        visited = set()
        temp_visited = set()
        result = []

        def visit(section_id: str):
            if section_id in temp_visited:
                # 检测到循环依赖
                return
            if section_id in visited:
                return

            temp_visited.add(section_id)

            if section_id in self.structure:
                for dep in self.structure[section_id].dependencies:
                    visit(dep)

            temp_visited.remove(section_id)
            visited.add(section_id)
            result.append(section_id)

        for section_id in self.structure.keys():
            if section_id not in visited:
                visit(section_id)

        return result

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'tree_id': self.tree_id,
            'concept': self.concept,
            'patent_type': self.patent_type.value,
            'strategy': self.strategy.value,
            'structure': {k: v.to_dict() for k, v in self.structure.items()},
            'total_estimated_words': self.total_estimated_words,
            'total_estimated_time': self.total_estimated_time,
            'creation_timestamp': self.creation_timestamp,
            'dependency_order': self.get_dependency_order()
        }


class PlannerAgent(BaseAgent):
    """规划智能体 - 负责专利生成的整体规划"""

    def __init__(self,
                 default_strategy: PlanningStrategy = PlanningStrategy.STANDARD,
                 enable_adaptive_planning: bool = True,
                 **kwargs):
        """
        初始化规划智能体

        Args:
            default_strategy: 默认规划策略
            enable_adaptive_planning: 是否启用自适应规划
            **kwargs: 基类参数
        """
        super().__init__(
            agent_name="PlannerAgent",
            capabilities=[AgentCapability.PLANNING, AgentCapability.REASONING],
            **kwargs
        )

        self.default_strategy = default_strategy
        self.enable_adaptive_planning = enable_adaptive_planning

        # 规划模板库
        self.planning_templates = self._load_planning_templates()
        self.section_requirements = self._load_section_requirements()

        # 规划标准配置
        self.planning_standards = {
            'min_sections': 4,
            'max_sections': 10,
            'word_count_targets': {
                'abstract': (150, 400),
                'background': (500, 1500),
                'summary': (400, 1000),
                'detailed_description': (800, 3000),
                'claims': (200, 1000),
                'drawings': (100, 300)
            },
            'complexity_factors': {
                'technical_complexity': 1.0,
                'legal_complexity': 1.0,
                'domain_complexity': 1.0
            }
        }

        # 规划统计
        self.planning_stats = {
            'total_plans_created': 0,
            'successful_plans': 0,
            'failed_plans': 0,
            'average_planning_time': 0.0,
            'average_sections_per_plan': 0.0,
            'strategy_usage': {strategy.value: 0 for strategy in PlanningStrategy}
        }

        self.logger.info(f"PlannerAgent初始化完成 - 默认策略: {default_strategy.value}")

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理规划请求的主方法

        Args:
            input_data: 包含concept、options等规划信息

        Returns:
            规划结果字典
        """
        start_time = datetime.now()

        try:
            # 验证输入数据
            if not self.validate_input(input_data):
                return self._create_error_result("输入数据验证失败")

            # 解析输入参数
            concept = input_data.get('concept', '')
            options = input_data.get('options', {})
            workflow_id = input_data.get('workflow_id', '')

            if not concept:
                return self._create_error_result("缺少必要参数: concept")

            self.logger.info(f"开始规划 - concept: {concept[:100]}...")

            # 分析concept和确定策略
            analysis_result = self._analyze_concept(concept, options)

            if not analysis_result['success']:
                return self._create_error_result(f"Concept分析失败: {analysis_result['error']}")

            # 选择规划策略
            strategy = self._select_planning_strategy(analysis_result, options)

            # 确定专利类型
            patent_type = self._determine_patent_type(analysis_result, options)

            # 创建PGTree结构
            pgtree_result = self._create_pgtree_structure(
                concept,
                analysis_result,
                strategy,
                patent_type,
                options
            )

            if not pgtree_result['success']:
                return self._create_error_result(f"PGTree创建失败: {pgtree_result['error']}")

            pgtree = pgtree_result['pgtree']

            # 优化规划
            optimization_result = self._optimize_planning(pgtree, analysis_result)

            if optimization_result['success']:
                pgtree = optimization_result['pgtree']

            # 验证规划质量
            validation_result = self._validate_planning(pgtree)

            # 记录统计信息
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_planning_stats(pgtree, strategy, processing_time, validation_result['valid'])

            # 记录token使用
            input_tokens, output_tokens = self._estimate_token_usage(input_data, pgtree)
            self.token_tracker.add_tokens(
                operation=OperationType.PLANNING,
                agent_name=self.agent_name,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                model_name=self.model_name,
                response_time=processing_time,
                workflow_id=workflow_id,
                success=validation_result['valid'],
                complexity_score=analysis_result.get('complexity_score', 1.0)
            )

            self.logger.info(f"规划完成 - 策略: {strategy.value}, section数: {len(pgtree.structure)}")

            return {
                'success': True,
                'pgtree': pgtree,
                'concept_analysis': analysis_result,
                'planning_metadata': {
                    'strategy': strategy.value,
                    'patent_type': patent_type.value,
                    'processing_time': processing_time,
                    'sections_count': len(pgtree.structure),
                    'total_estimated_words': pgtree.total_estimated_words,
                    'total_estimated_time': pgtree.total_estimated_time
                },
                'validation_result': validation_result,
                'plan_details': pgtree.to_dict()
            }

        except Exception as e:
            self.logger.error(f"规划处理失败: {e}")
            return self._create_error_result(f"规划异常: {e}")

    def _analyze_concept(self, concept: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """分析专利概念"""
        try:
            self.logger.debug("开始分析concept")

            # 构建分析prompt
            analysis_prompt = self._build_concept_analysis_prompt(concept, options)

            # 调用LLM进行分析
            analysis_response = self._generate_response(
                prompt=analysis_prompt,
                system_prompt=self._get_concept_analysis_system_prompt(),
                temperature=0.5
            )

            # 解析分析结果
            parsed_analysis = self._parse_concept_analysis(analysis_response)

            # 计算复杂度评分
            complexity_score = self._calculate_complexity_score(concept, parsed_analysis)

            return {
                'success': True,
                'technical_field': parsed_analysis.get('technical_field', ''),
                'main_innovation': parsed_analysis.get('main_innovation', ''),
                'technical_problems': parsed_analysis.get('technical_problems', []),
                'solution_approach': parsed_analysis.get('solution_approach', ''),
                'key_features': parsed_analysis.get('key_features', []),
                'application_domains': parsed_analysis.get('application_domains', []),
                'complexity_score': complexity_score,
                'estimated_novelty': parsed_analysis.get('estimated_novelty', 0.5),
                'analysis_confidence': parsed_analysis.get('analysis_confidence', 0.7)
            }

        except Exception as e:
            self.logger.error(f"Concept分析失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _build_concept_analysis_prompt(self, concept: str, options: Dict[str, Any]) -> str:
        """构建concept分析prompt"""
        prompt = f"""
请分析以下专利概念，提取关键信息：

专利概念：
{concept}

请从以下方面进行分析：

1. 技术领域：该发明属于哪个技术领域？
2. 主要创新点：核心的技术创新是什么？
3. 技术问题：要解决的主要技术问题有哪些？
4. 解决方案：采用什么技术方案来解决问题？
5. 关键特征：技术方案的关键技术特征有哪些？
6. 应用领域：可能的应用领域和场景有哪些？
7. 复杂程度：技术复杂程度评估（1-10分）
8. 新颖性预估：与现有技术相比的新颖性程度（0-1）

请以JSON格式返回分析结果：
{{
    "technical_field": "技术领域",
    "main_innovation": "主要创新点",
    "technical_problems": ["问题1", "问题2"],
    "solution_approach": "解决方案描述",
    "key_features": ["特征1", "特征2"],
    "application_domains": ["应用领域1", "应用领域2"],
    "complexity_score": 7,
    "estimated_novelty": 0.8,
    "analysis_confidence": 0.9
}}
        """

        # 添加用户指定的分析要求
        if options.get('analysis_focus'):
            prompt += f"\n\n特别关注：{options['analysis_focus']}"

        return prompt

    def _get_concept_analysis_system_prompt(self) -> str:
        """获取concept分析的system prompt"""
        return """
你是一名资深的专利代理师和技术专家，具有丰富的专利分析经验。

你的任务是：
1. 深入理解技术概念的核心内容
2. 准确识别技术创新点和解决的问题
3. 评估技术方案的复杂程度和新颖性
4. 为后续的专利撰写提供准确的技术分析

分析原则：
- 保持客观和专业
- 注重技术细节的准确性
- 考虑专利申请的实际需求
- 提供结构化的分析结果
        """

    def _parse_concept_analysis(self, analysis_response: str) -> Dict[str, Any]:
        """解析concept分析结果"""
        try:
            # 尝试提取JSON内容
            json_match = re.search(r'\{.*\}', analysis_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # 如果没有找到JSON，尝试结构化解析
                return self._fallback_parse_analysis(analysis_response)

        except json.JSONDecodeError:
            self.logger.warning("JSON解析失败，使用fallback解析")
            return self._fallback_parse_analysis(analysis_response)

    def _fallback_parse_analysis(self, analysis_response: str) -> Dict[str, Any]:
        """Fallback解析方法"""
        lines = analysis_response.split('\n')
        result = {
            'technical_field': '',
            'main_innovation': '',
            'technical_problems': [],
            'solution_approach': '',
            'key_features': [],
            'application_domains': [],
            'complexity_score': 5,
            'estimated_novelty': 0.5,
            'analysis_confidence': 0.6
        }

        # 简化的文本解析
        for line in lines:
            line = line.strip()
            if '技术领域' in line:
                result['technical_field'] = line.split('：')[-1] if '：' in line else line
            elif '创新点' in line or '创新' in line:
                result['main_innovation'] = line.split('：')[-1] if '：' in line else line

        return result

    def _calculate_complexity_score(self, concept: str, analysis: Dict[str, Any]) -> float:
        """计算复杂度评分"""
        score = 1.0

        # 基于文本长度
        if len(concept) > 1000:
            score += 1.0
        elif len(concept) > 500:
            score += 0.5

        # 基于技术特征数量
        key_features = analysis.get('key_features', [])
        score += len(key_features) * 0.3

        # 基于技术问题复杂性
        problems = analysis.get('technical_problems', [])
        score += len(problems) * 0.2

        # 基于分析结果中的复杂度评分
        if 'complexity_score' in analysis:
            score = (score + analysis['complexity_score']) / 2

        return min(10.0, max(1.0, score))

    def _select_planning_strategy(self,
                                  analysis_result: Dict[str, Any],
                                  options: Dict[str, Any]) -> PlanningStrategy:
        """选择规划策略"""
        # 用户指定策略优先
        if 'planning_strategy' in options:
            try:
                return PlanningStrategy(options['planning_strategy'])
            except ValueError:
                self.logger.warning(f"无效的规划策略: {options['planning_strategy']}")

        # 自适应策略选择
        if self.enable_adaptive_planning:
            complexity = analysis_result.get('complexity_score', 5.0)
            novelty = analysis_result.get('estimated_novelty', 0.5)

            if complexity >= 8.0 or novelty >= 0.8:
                return PlanningStrategy.DETAILED
            elif complexity <= 3.0 and novelty <= 0.3:
                return PlanningStrategy.SIMPLIFIED
            else:
                return PlanningStrategy.STANDARD

        return self.default_strategy

    def _determine_patent_type(self,
                               analysis_result: Dict[str, Any],
                               options: Dict[str, Any]) -> PatentType:
        """确定专利类型"""
        # 用户指定类型优先
        if 'patent_type' in options:
            try:
                return PatentType(options['patent_type'])
            except ValueError:
                self.logger.warning(f"无效的专利类型: {options['patent_type']}")

        # 基于技术领域自动判断
        technical_field = analysis_result.get('technical_field', '').lower()

        if '软件' in technical_field or 'software' in technical_field:
            return PatentType.SOFTWARE
        elif '方法' in technical_field or 'method' in technical_field:
            return PatentType.METHOD
        elif '外观' in technical_field or 'design' in technical_field:
            return PatentType.DESIGN
        elif '实用新型' in technical_field or 'utility' in technical_field:
            return PatentType.UTILITY_MODEL
        else:
            return PatentType.INVENTION

    def _create_pgtree_structure(self,
                                 concept: str,
                                 analysis_result: Dict[str, Any],
                                 strategy: PlanningStrategy,
                                 patent_type: PatentType,
                                 options: Dict[str, Any]) -> Dict[str, Any]:
        """创建PGTree结构"""
        try:
            # 获取section模板
            template = self.planning_templates[strategy.value][patent_type.value]

            # 创建section计划
            section_plans = {}
            total_words = 0
            total_time = 0

            for section_config in template['sections']:
                section_plan = self._create_section_plan(
                    section_config,
                    analysis_result,
                    options
                )

                section_plans[section_plan.section_id] = section_plan
                total_words += section_plan.estimated_words
                total_time += section_plan.estimated_time_minutes

            # 创建PGTree对象
            tree_id = f"pgtree_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            pgtree = PGTree(
                tree_id=tree_id,
                concept=concept,
                patent_type=patent_type,
                strategy=strategy,
                structure=section_plans,
                total_estimated_words=total_words,
                total_estimated_time=total_time,
                creation_timestamp=datetime.now().isoformat()
            )

            return {
                'success': True,
                'pgtree': pgtree
            }

        except Exception as e:
            self.logger.error(f"PGTree结构创建失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _create_section_plan(self,
                             section_config: Dict[str, Any],
                             analysis_result: Dict[str, Any],
                             options: Dict[str, Any]) -> SectionPlan:
        """创建单个section的规划"""
        section_id = section_config['section_id']

        # 获取基础配置
        base_words = section_config.get('base_words', 300)
        base_time = section_config.get('base_time_minutes', 30)

        # 根据复杂度调整估算
        complexity = analysis_result.get('complexity_score', 5.0)
        complexity_factor = complexity / 5.0

        estimated_words = int(base_words * complexity_factor)
        estimated_time = int(base_time * complexity_factor)

        # 应用word count限制
        if section_id in self.planning_standards['word_count_targets']:
            min_words, max_words = self.planning_standards['word_count_targets'][section_id]
            estimated_words = max(min_words, min(max_words, estimated_words))

        # 生成requirements和writing guide
        requirements = self._generate_section_requirements(section_id, analysis_result)
        writing_guide = self.section_requirements.get(section_id, {}).get('writing_guide', '')

        return SectionPlan(
            section_id=section_id,
            section_name=section_config['section_name'],
            priority=PriorityLevel(section_config.get('priority', 'medium')),
            estimated_words=estimated_words,
            dependencies=section_config.get('dependencies', []),
            requirements=requirements,
            writing_guide=writing_guide,
            complexity_level=complexity_factor,
            estimated_time_minutes=estimated_time
        )

    def _generate_section_requirements(self,
                                       section_id: str,
                                       analysis_result: Dict[str, Any]) -> str:
        """生成section特定的要求"""
        requirements = []

        if section_id == 'abstract':
            requirements.append(f"简要说明技术领域：{analysis_result.get('technical_field', '')}")
            requirements.append(f"核心技术方案：{analysis_result.get('solution_approach', '')}")

        elif section_id == 'background':
            problems = analysis_result.get('technical_problems', [])
            if problems:
                requirements.append(f"重点说明以下技术问题：{'; '.join(problems[:3])}")

        elif section_id == 'summary':
            innovation = analysis_result.get('main_innovation', '')
            if innovation:
                requirements.append(f"突出主要创新点：{innovation}")

        elif section_id == 'claims':
            features = analysis_result.get('key_features', [])
            if features:
                requirements.append(f"确保包含关键技术特征：{'; '.join(features[:5])}")

        return '\n'.join(requirements)

    def _optimize_planning(self, pgtree: PGTree, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """优化规划"""
        try:
            optimizations_applied = []

            # 依赖关系优化
            dependency_optimization = self._optimize_dependencies(pgtree)
            if dependency_optimization['optimized']:
                optimizations_applied.append("依赖关系优化")

            # 资源分配优化
            resource_optimization = self._optimize_resource_allocation(pgtree, analysis_result)
            if resource_optimization['optimized']:
                optimizations_applied.append("资源分配优化")

            # 优先级调整
            priority_optimization = self._optimize_priorities(pgtree)
            if priority_optimization['optimized']:
                optimizations_applied.append("优先级优化")

            return {
                'success': True,
                'pgtree': pgtree,
                'optimizations_applied': optimizations_applied
            }

        except Exception as e:
            self.logger.error(f"规划优化失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'pgtree': pgtree
            }

    def _optimize_dependencies(self, pgtree: PGTree) -> Dict[str, Any]:
        """优化依赖关系"""
        optimized = False

        # 检查循环依赖
        try:
            dependency_order = pgtree.get_dependency_order()

            # 如果成功获得依赖顺序，说明没有循环依赖
            # 可以进一步优化依赖关系的合理性

            # 移除不必要的间接依赖
            for section_id, section_plan in pgtree.structure.items():
                original_deps = section_plan.dependencies.copy()
                optimized_deps = self._remove_redundant_dependencies(
                    section_plan.dependencies,
                    pgtree.structure
                )

                if optimized_deps != original_deps:
                    section_plan.dependencies = optimized_deps
                    optimized = True

        except Exception as e:
            self.logger.warning(f"依赖关系优化失败: {e}")

        return {'optimized': optimized}

    def _remove_redundant_dependencies(self,
                                       dependencies: List[str],
                                       structure: Dict[str, SectionPlan]) -> List[str]:
        """移除冗余依赖"""
        # 简化实现：移除间接依赖
        direct_deps = dependencies.copy()

        for dep in dependencies:
            if dep in structure:
                # 如果依赖的section也依赖于当前dependencies中的其他项，
                # 则可以移除间接依赖
                dep_dependencies = structure[dep].dependencies
                for indirect_dep in dep_dependencies:
                    if indirect_dep in direct_deps and indirect_dep != dep:
                        # 发现间接依赖，可以移除
                        if indirect_dep in direct_deps:
                            direct_deps.remove(indirect_dep)

        return direct_deps

    def _optimize_resource_allocation(self,
                                      pgtree: PGTree,
                                      analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """优化资源分配"""
        optimized = False

        # 根据section重要性重新分配word count
        total_importance_score = 0
        importance_scores = {}

        for section_id, section_plan in pgtree.structure.items():
            # 计算重要性评分
            importance = self._calculate_section_importance(section_id, analysis_result)
            importance_scores[section_id] = importance
            total_importance_score += importance

        # 重新分配word count
        target_total_words = pgtree.total_estimated_words

        for section_id, section_plan in pgtree.structure.items():
            if total_importance_score > 0:
                importance_ratio = importance_scores[section_id] / total_importance_score
                new_word_count = int(target_total_words * importance_ratio)

                # 应用最小/最大限制
                if section_id in self.planning_standards['word_count_targets']:
                    min_words, max_words = self.planning_standards['word_count_targets'][section_id]
                    new_word_count = max(min_words, min(max_words, new_word_count))

                if new_word_count != section_plan.estimated_words:
                    section_plan.estimated_words = new_word_count
                    # 相应调整时间估算
                    section_plan.estimated_time_minutes = int(new_word_count / 10)  # 假设每分钟10字
                    optimized = True

        # 重新计算总计
        if optimized:
            pgtree.total_estimated_words = sum(plan.estimated_words for plan in pgtree.structure.values())
            pgtree.total_estimated_time = sum(plan.estimated_time_minutes for plan in pgtree.structure.values())

        return {'optimized': optimized}

    def _calculate_section_importance(self, section_id: str, analysis_result: Dict[str, Any]) -> float:
        """计算section重要性"""
        base_importance = {
            'claims': 10.0,  # 权利要求最重要
            'abstract': 8.0,  # 摘要很重要
            'summary': 7.0,  # 发明内容重要
            'detailed_description': 6.0,  # 具体实施方式
            'background': 5.0,  # 背景技术
            'drawings': 3.0  # 附图说明
        }

        importance = base_importance.get(section_id, 5.0)

        # 根据技术复杂度调整
        complexity = analysis_result.get('complexity_score', 5.0)
        if section_id in ['detailed_description', 'claims'] and complexity > 7.0:
            importance *= 1.2

        return importance

    def _optimize_priorities(self, pgtree: PGTree) -> Dict[str, Any]:
        """优化优先级"""
        optimized = False

        # 基于依赖关系调整优先级
        dependency_order = pgtree.get_dependency_order()

        for i, section_id in enumerate(dependency_order):
            if section_id in pgtree.structure:
                section_plan = pgtree.structure[section_id]

                # 依赖链中越早的section优先级越高
                if i < len(dependency_order) / 3:
                    new_priority = PriorityLevel.HIGH
                elif i < len(dependency_order) * 2 / 3:
                    new_priority = PriorityLevel.MEDIUM
                else:
                    new_priority = PriorityLevel.LOW

                if section_plan.priority != new_priority:
                    section_plan.priority = new_priority
                    optimized = True

        return {'optimized': optimized}

    def _validate_planning(self, pgtree: PGTree) -> Dict[str, Any]:
        """验证规划质量"""
        validation_issues = []
        warnings = []

        # 检查必要section
        required_sections = ['abstract', 'claims']
        for req_section in required_sections:
            if req_section not in pgtree.structure:
                validation_issues.append(f"缺少必要section: {req_section}")

        # 检查section数量
        section_count = len(pgtree.structure)
        if section_count < self.planning_standards['min_sections']:
            validation_issues.append(f"Section数量过少: {section_count} < {self.planning_standards['min_sections']}")
        elif section_count > self.planning_standards['max_sections']:
            warnings.append(f"Section数量较多: {section_count} > {self.planning_standards['max_sections']}")

        # 检查依赖关系
        try:
            dependency_order = pgtree.get_dependency_order()
            if len(dependency_order) != len(pgtree.structure):
                validation_issues.append("存在循环依赖或孤立节点")
        except Exception:
            validation_issues.append("依赖关系验证失败")

        # 检查word count合理性
        total_words = pgtree.total_estimated_words
        if total_words < 1000:
            warnings.append(f"总字数较少: {total_words}")
        elif total_words > 10000:
            warnings.append(f"总字数较多: {total_words}")

        is_valid = len(validation_issues) == 0

        return {
            'valid': is_valid,
            'issues': validation_issues,
            'warnings': warnings,
            'quality_score': self._calculate_planning_quality_score(
                len(validation_issues),
                len(warnings),
                pgtree
            )
        }

    def _calculate_planning_quality_score(self,
                                          issues_count: int,
                                          warnings_count: int,
                                          pgtree: PGTree) -> float:
        """计算规划质量评分"""
        base_score = 10.0

        # 问题扣分
        base_score -= issues_count * 2.0
        base_score -= warnings_count * 0.5

        # 结构完整性加分
        if len(pgtree.structure) >= 5:
            base_score += 1.0

        # 依赖关系合理性加分
        try:
            dependency_order = pgtree.get_dependency_order()
            if len(dependency_order) == len(pgtree.structure):
                base_score += 1.0
        except Exception:
            pass

        return max(0.0, min(10.0, base_score))

    def _update_planning_stats(self,
                               pgtree: PGTree,
                               strategy: PlanningStrategy,
                               processing_time: float,
                               success: bool):
        """更新规划统计信息"""
        self.planning_stats['total_plans_created'] += 1

        if success:
            self.planning_stats['successful_plans'] += 1
        else:
            self.planning_stats['failed_plans'] += 1

        # 更新平均规划时间
        total_plans = self.planning_stats['total_plans_created']
        current_avg = self.planning_stats['average_planning_time']
        new_avg = ((current_avg * (total_plans - 1)) + processing_time) / total_plans
        self.planning_stats['average_planning_time'] = new_avg

        # 更新平均section数量
        sections_count = len(pgtree.structure)
        current_avg_sections = self.planning_stats['average_sections_per_plan']
        new_avg_sections = ((current_avg_sections * (total_plans - 1)) + sections_count) / total_plans
        self.planning_stats['average_sections_per_plan'] = new_avg_sections

        # 更新策略使用统计
        self.planning_stats['strategy_usage'][strategy.value] += 1

    def _estimate_token_usage(self, input_data: Dict[str, Any], pgtree: PGTree) -> tuple:
        """估算token使用量"""
        # 输入token估算
        input_text = str(input_data)
        input_tokens = int(len(input_text) * 0.8)

        # 输出token估算
        output_text = json.dumps(pgtree.to_dict(), ensure_ascii=False)
        output_tokens = int(len(output_text) * 0.8)

        return input_tokens, output_tokens

    def _load_planning_templates(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """加载规划模板"""
        return {
            'standard': {
                'invention': {
                    'description': '标准发明专利模板',
                    'sections': [
                        {
                            'section_id': 'abstract',
                            'section_name': '摘要',
                            'priority': 'high',
                            'base_words': 250,
                            'base_time_minutes': 20,
                            'dependencies': []
                        },
                        {
                            'section_id': 'background',
                            'section_name': '背景技术',
                            'priority': 'high',
                            'base_words': 800,
                            'base_time_minutes': 40,
                            'dependencies': []
                        },
                        {
                            'section_id': 'summary',
                            'section_name': '发明内容',
                            'priority': 'high',
                            'base_words': 600,
                            'base_time_minutes': 35,
                            'dependencies': ['background']
                        },
                        {
                            'section_id': 'detailed_description',
                            'section_name': '具体实施方式',
                            'priority': 'medium',
                            'base_words': 1500,
                            'base_time_minutes': 60,
                            'dependencies': ['summary']
                        },
                        {
                            'section_id': 'claims',
                            'section_name': '权利要求书',
                            'priority': 'critical',
                            'base_words': 400,
                            'base_time_minutes': 45,
                            'dependencies': ['summary', 'detailed_description']
                        }
                    ]
                },
                'utility_model': {
                    'description': '实用新型专利模板',
                    'sections': [
                        {
                            'section_id': 'abstract',
                            'section_name': '摘要',
                            'priority': 'high',
                            'base_words': 200,
                            'base_time_minutes': 15,
                            'dependencies': []
                        },
                        {
                            'section_id': 'background',
                            'section_name': '背景技术',
                            'priority': 'medium',
                            'base_words': 500,
                            'base_time_minutes': 25,
                            'dependencies': []
                        },
                        {
                            'section_id': 'summary',
                            'section_name': '发明内容',
                            'priority': 'high',
                            'base_words': 400,
                            'base_time_minutes': 25,
                            'dependencies': ['background']
                        },
                        {
                            'section_id': 'detailed_description',
                            'section_name': '具体实施方式',
                            'priority': 'medium',
                            'base_words': 800,
                            'base_time_minutes': 35,
                            'dependencies': ['summary']
                        },
                        {
                            'section_id': 'claims',
                            'section_name': '权利要求书',
                            'priority': 'critical',
                            'base_words': 300,
                            'base_time_minutes': 30,
                            'dependencies': ['summary']
                        }
                    ]
                },
                'software': {
                    'description': '软件专利模板',
                    'sections': [
                        {
                            'section_id': 'abstract',
                            'section_name': '摘要',
                            'priority': 'high',
                            'base_words': 300,
                            'base_time_minutes': 20,
                            'dependencies': []
                        },
                        {
                            'section_id': 'background',
                            'section_name': '背景技术',
                            'priority': 'high',
                            'base_words': 1000,
                            'base_time_minutes': 45,
                            'dependencies': []
                        },
                        {
                            'section_id': 'summary',
                            'section_name': '发明内容',
                            'priority': 'high',
                            'base_words': 800,
                            'base_time_minutes': 40,
                            'dependencies': ['background']
                        },
                        {
                            'section_id': 'detailed_description',
                            'section_name': '具体实施方式',
                            'priority': 'high',
                            'base_words': 2000,
                            'base_time_minutes': 80,
                            'dependencies': ['summary']
                        },
                        {
                            'section_id': 'claims',
                            'section_name': '权利要求书',
                            'priority': 'critical',
                            'base_words': 500,
                            'base_time_minutes': 50,
                            'dependencies': ['summary', 'detailed_description']
                        }
                    ]
                },
                'method': {
                    'description': '方法专利模板',
                    'sections': [
                        {
                            'section_id': 'abstract',
                            'section_name': '摘要',
                            'priority': 'high',
                            'base_words': 250,
                            'base_time_minutes': 20,
                            'dependencies': []
                        },
                        {
                            'section_id': 'background',
                            'section_name': '背景技术',
                            'priority': 'high',
                            'base_words': 700,
                            'base_time_minutes': 35,
                            'dependencies': []
                        },
                        {
                            'section_id': 'summary',
                            'section_name': '发明内容',
                            'priority': 'high',
                            'base_words': 700,
                            'base_time_minutes': 40,
                            'dependencies': ['background']
                        },
                        {
                            'section_id': 'detailed_description',
                            'section_name': '具体实施方式',
                            'priority': 'high',
                            'base_words': 1200,
                            'base_time_minutes': 55,
                            'dependencies': ['summary']
                        },
                        {
                            'section_id': 'claims',
                            'section_name': '权利要求书',
                            'priority': 'critical',
                            'base_words': 350,
                            'base_time_minutes': 40,
                            'dependencies': ['summary', 'detailed_description']
                        }
                    ]
                },
                'design': {
                    'description': '外观设计专利模板',
                    'sections': [
                        {
                            'section_id': 'abstract',
                            'section_name': '摘要',
                            'priority': 'high',
                            'base_words': 150,
                            'base_time_minutes': 15,
                            'dependencies': []
                        },
                        {
                            'section_id': 'summary',
                            'section_name': '设计要点',
                            'priority': 'high',
                            'base_words': 300,
                            'base_time_minutes': 20,
                            'dependencies': []
                        },
                        {
                            'section_id': 'detailed_description',
                            'section_name': '设计说明',
                            'priority': 'medium',
                            'base_words': 500,
                            'base_time_minutes': 25,
                            'dependencies': ['summary']
                        }
                    ]
                }
            },
            'detailed': {
                # 详细策略模板（更多section，更高要求）
                'invention': {
                    'description': '详细发明专利模板',
                    'sections': [
                        {
                            'section_id': 'technical_field',
                            'section_name': '技术领域',
                            'priority': 'medium',
                            'base_words': 200,
                            'base_time_minutes': 15,
                            'dependencies': []
                        },
                        {
                            'section_id': 'abstract',
                            'section_name': '摘要',
                            'priority': 'high',
                            'base_words': 350,
                            'base_time_minutes': 25,
                            'dependencies': ['technical_field']
                        },
                        {
                            'section_id': 'background',
                            'section_name': '背景技术',
                            'priority': 'high',
                            'base_words': 1200,
                            'base_time_minutes': 50,
                            'dependencies': ['technical_field']
                        },
                        {
                            'section_id': 'summary',
                            'section_name': '发明内容',
                            'priority': 'high',
                            'base_words': 800,
                            'base_time_minutes': 45,
                            'dependencies': ['background']
                        },
                        {
                            'section_id': 'detailed_description',
                            'section_name': '具体实施方式',
                            'priority': 'medium',
                            'base_words': 2000,
                            'base_time_minutes': 80,
                            'dependencies': ['summary']
                        },
                        {
                            'section_id': 'claims',
                            'section_name': '权利要求书',
                            'priority': 'critical',
                            'base_words': 600,
                            'base_time_minutes': 60,
                            'dependencies': ['summary', 'detailed_description']
                        },
                        {
                            'section_id': 'drawings',
                            'section_name': '附图说明',
                            'priority': 'low',
                            'base_words': 200,
                            'base_time_minutes': 20,
                            'dependencies': ['detailed_description']
                        }
                    ]
                }
            },
            'simplified': {
                # 简化策略模板（较少section，快速生成）
                'invention': {
                    'description': '简化发明专利模板',
                    'sections': [
                        {
                            'section_id': 'abstract',
                            'section_name': '摘要',
                            'priority': 'high',
                            'base_words': 200,
                            'base_time_minutes': 15,
                            'dependencies': []
                        },
                        {
                            'section_id': 'summary',
                            'section_name': '发明内容',
                            'priority': 'high',
                            'base_words': 400,
                            'base_time_minutes': 25,
                            'dependencies': []
                        },
                        {
                            'section_id': 'claims',
                            'section_name': '权利要求书',
                            'priority': 'critical',
                            'base_words': 250,
                            'base_time_minutes': 30,
                            'dependencies': ['summary']
                        }
                    ]
                }
            }
        }

    def _load_section_requirements(self) -> Dict[str, Dict[str, str]]:
        """加载section要求配置"""
        return {
            'abstract': {
                'writing_guide': '简洁明了地概述发明内容，包含技术领域、技术问题、解决方案和有益效果',
                'key_elements': ['技术领域', '技术问题', '解决方案', '有益效果']
            },
            'background': {
                'writing_guide': '客观描述相关技术领域的现状，指出现有技术的不足',
                'key_elements': ['技术现状', '现有技术', '存在问题']
            },
            'summary': {
                'writing_guide': '明确说明发明目的，详述技术方案，突出创新点',
                'key_elements': ['发明目的', '技术方案', '创新点', '有益效果']
            },
            'detailed_description': {
                'writing_guide': '结合实施例详细说明发明的实现方法，提供充分的技术细节',
                'key_elements': ['实施例', '技术细节', '实现方法']
            },
            'claims': {
                'writing_guide': '准确定义保护范围，逻辑清晰，层次分明',
                'key_elements': ['独立权利要求', '从属权利要求', '技术特征']
            }
        }

    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            'success': False,
            'error': error_message,
            'agent_name': self.agent_name,
            'timestamp': datetime.now().isoformat()
        }

    def get_planning_statistics(self) -> Dict[str, Any]:
        """获取规划统计信息"""
        stats = self.planning_stats.copy()

        # 计算成功率
        if stats['total_plans_created'] > 0:
            stats['success_rate'] = stats['successful_plans'] / stats['total_plans_created']
        else:
            stats['success_rate'] = 0.0

        # 添加配置信息
        stats['configuration'] = {
            'default_strategy': self.default_strategy.value,
            'adaptive_planning': self.enable_adaptive_planning,
            'planning_standards': self.planning_standards
        }

        return stats

    def reset_statistics(self):
        """重置统计信息"""
        self.planning_stats = {
            'total_plans_created': 0,
            'successful_plans': 0,
            'failed_plans': 0,
            'average_planning_time': 0.0,
            'average_sections_per_plan': 0.0,
            'strategy_usage': {strategy.value: 0 for strategy in PlanningStrategy}
        }

        self.logger.info(f"{self.agent_name} 统计信息已重置")
