"""
PGTree处理器 - 专利生成树结构的管理和操作工具
"""

import logging
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
from enum import Enum
from datetime import datetime
import json
import copy
from dataclasses import dataclass, field
import threading
from concurrent.futures import Thread<PERSON><PERSON>Executor, Future
import uuid


class NodeStatus(Enum):
    """节点状态枚举"""
    PENDING = "pending"  # 待处理
    IN_PROGRESS = "in_progress"  # 处理中
    COMPLETED = "completed"  # 已完成
    ERROR = "error"  # 错误
    SKIPPED = "skipped"  # 跳过
    BLOCKED = "blocked"  # 被阻塞


class NodeType(Enum):
    """节点类型枚举"""
    SECTION = "section"  # 普通section节点
    SUBPROCESS = "subprocess"  # 子流程节点
    DECISION = "decision"  # 决策节点
    PARALLEL = "parallel"  # 并行节点
    MERGE = "merge"  # 合并节点


class ExecutionMode(Enum):
    """执行模式枚举"""
    SEQUENTIAL = "sequential"  # 顺序执行
    PARALLEL = "parallel"  # 并行执行
    ADAPTIVE = "adaptive"  # 自适应执行


@dataclass
class NodeMetrics:
    """节点指标数据类"""
    execution_time: float = 0.0
    retry_count: int = 0
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    quality_score: float = 0.0
    complexity_score: float = 1.0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'execution_time': self.execution_time,
            'retry_count': self.retry_count,
            'memory_usage': self.memory_usage,
            'cpu_usage': self.cpu_usage,
            'quality_score': self.quality_score,
            'complexity_score': self.complexity_score
        }


@dataclass
class PGTreeNode:
    """PGTree节点数据类"""
    node_id: str
    section_name: str
    node_type: NodeType = NodeType.SECTION
    status: NodeStatus = NodeStatus.PENDING
    priority: str = "medium"
    content: str = ""
    dependencies: List[str] = field(default_factory=list)
    children: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    metrics: NodeMetrics = field(default_factory=NodeMetrics)

    # 执行相关
    assigned_agent: str = ""
    execution_start_time: Optional[datetime] = None
    execution_end_time: Optional[datetime] = None

    # 内容相关
    word_count: int = 0
    estimated_time: int = 0
    requirements: str = ""
    writing_guide: str = ""

    # 版本控制
    version: int = 1
    history: List[Dict[str, Any]] = field(default_factory=list)

    def __post_init__(self):
        """初始化后处理"""
        if self.content:
            self.word_count = len(self.content.split())

        # 创建时间戳
        if 'created_at' not in self.metadata:
            self.metadata['created_at'] = datetime.now().isoformat()

    def update_content(self, new_content: str, agent_name: str = ""):
        """更新节点内容"""
        # 保存历史版本
        if self.content:
            self.history.append({
                'version': self.version,
                'content': self.content,
                'timestamp': datetime.now().isoformat(),
                'agent': self.assigned_agent
            })

        # 更新内容
        self.content = new_content
        self.word_count = len(new_content.split()) if new_content else 0
        self.version += 1

        if agent_name:
            self.assigned_agent = agent_name

        self.metadata['updated_at'] = datetime.now().isoformat()

    def start_execution(self, agent_name: str = ""):
        """开始执行"""
        self.status = NodeStatus.IN_PROGRESS
        self.execution_start_time = datetime.now()

        if agent_name:
            self.assigned_agent = agent_name

        self.metadata['execution_started_at'] = self.execution_start_time.isoformat()

    def complete_execution(self, success: bool = True, error_message: str = ""):
        """完成执行"""
        self.execution_end_time = datetime.now()

        if success:
            self.status = NodeStatus.COMPLETED
        else:
            self.status = NodeStatus.ERROR
            self.metadata['error_message'] = error_message

        # 计算执行时间
        if self.execution_start_time:
            self.metrics.execution_time = (
                    self.execution_end_time - self.execution_start_time
            ).total_seconds()

        self.metadata['execution_completed_at'] = self.execution_end_time.isoformat()

    def can_execute(self, completed_nodes: set) -> bool:
        """检查是否可以执行（依赖是否满足）"""
        if self.status in [NodeStatus.COMPLETED, NodeStatus.ERROR, NodeStatus.IN_PROGRESS]:
            return False

        # 检查依赖是否都已完成
        return all(dep_id in completed_nodes for dep_id in self.dependencies)

    def get_execution_duration(self) -> float:
        """获取执行持续时间"""
        if self.execution_start_time and self.execution_end_time:
            return (self.execution_end_time - self.execution_start_time).total_seconds()
        elif self.execution_start_time:
            return (datetime.now() - self.execution_start_time).total_seconds()
        return 0.0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'node_id': self.node_id,
            'section_name': self.section_name,
            'node_type': self.node_type.value,
            'status': self.status.value,
            'priority': self.priority,
            'content': self.content,
            'dependencies': self.dependencies,
            'children': self.children,
            'metadata': self.metadata,
            'metrics': self.metrics.to_dict(),
            'assigned_agent': self.assigned_agent,
            'word_count': self.word_count,
            'estimated_time': self.estimated_time,
            'requirements': self.requirements,
            'writing_guide': self.writing_guide,
            'version': self.version,
            'execution_duration': self.get_execution_duration()
        }


class PGTreeHandler:
    """PGTree处理器 - 管理专利生成的树形工作流"""

    def __init__(self,
                 tree_id: str = None,
                 execution_mode: ExecutionMode = ExecutionMode.SEQUENTIAL,
                 max_parallel_nodes: int = 3,
                 enable_metrics: bool = True,
                 auto_save: bool = True):
        """
        初始化PGTree处理器

        Args:
            tree_id: 树的唯一标识符
            execution_mode: 执行模式
            max_parallel_nodes: 最大并行节点数
            enable_metrics: 是否启用指标收集
            auto_save: 是否自动保存
        """
        self.tree_id = tree_id or f"pgtree_{uuid.uuid4().hex[:8]}"
        self.execution_mode = execution_mode
        self.max_parallel_nodes = max_parallel_nodes
        self.enable_metrics = enable_metrics
        self.auto_save = auto_save

        # 核心数据结构
        self.nodes: Dict[str, PGTreeNode] = {}
        self.edges: List[Tuple[str, str]] = []  # (from_node, to_node)

        # 执行状态
        self.execution_state = {
            'started_at': None,
            'completed_at': None,
            'total_nodes': 0,
            'completed_nodes': 0,
            'failed_nodes': 0,
            'current_phase': 'initialization'
        }

        # 并发控制
        self._lock = threading.RLock()
        self._executor = ThreadPoolExecutor(max_workers=max_parallel_nodes)
        self._running_tasks: Dict[str, Future] = {}

        # 回调函数
        self._node_callbacks: Dict[str, List[Callable]] = {}
        self._global_callbacks: List[Callable] = []

        # 缓存和优化
        self._dependency_cache: Dict[str, List[str]] = {}
        self._execution_order_cache: Optional[List[str]] = None
        self._cache_valid = True

        # 日志记录器
        self.logger = logging.getLogger(f"AutoPatent.PGTreeHandler.{self.tree_id}")

        # 统计信息
        self.stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_execution_time': 0.0,
            'total_nodes_processed': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }

        self.logger.info(f"PGTreeHandler初始化完成 - ID: {self.tree_id}")

    def add_node(self, node: PGTreeNode) -> bool:
        """
        添加节点到PGTree

        Args:
            node: 要添加的节点

        Returns:
            是否添加成功
        """
        with self._lock:
            try:
                if node.node_id in self.nodes:
                    self.logger.warning(f"节点 {node.node_id} 已存在，将被覆盖")

                self.nodes[node.node_id] = node

                # 添加边（依赖关系）
                for dep_id in node.dependencies:
                    if dep_id in self.nodes:
                        self._add_edge(dep_id, node.node_id)

                # 更新统计
                self.execution_state['total_nodes'] = len(self.nodes)

                # 清除缓存
                self._invalidate_cache()

                self.logger.debug(f"节点已添加: {node.node_id}")
                return True

            except Exception as e:
                self.logger.error(f"添加节点失败: {e}")
                return False

    def remove_node(self, node_id: str) -> bool:
        """
        移除节点

        Args:
            node_id: 节点ID

        Returns:
            是否移除成功
        """
        with self._lock:
            try:
                if node_id not in self.nodes:
                    self.logger.warning(f"节点 {node_id} 不存在")
                    return False

                # 移除相关边
                self.edges = [(from_id, to_id) for from_id, to_id in self.edges
                              if from_id != node_id and to_id != node_id]

                # 移除节点
                del self.nodes[node_id]

                # 更新其他节点的依赖
                for node in self.nodes.values():
                    if node_id in node.dependencies:
                        node.dependencies.remove(node_id)
                    if node_id in node.children:
                        node.children.remove(node_id)

                # 清除缓存
                self._invalidate_cache()

                self.logger.debug(f"节点已移除: {node_id}")
                return True

            except Exception as e:
                self.logger.error(f"移除节点失败: {e}")
                return False

    def update_node_content(self,
                            node_id: str,
                            content: str,
                            status: NodeStatus = None,
                            agent_name: str = "") -> bool:
        """
        更新节点内容

        Args:
            node_id: 节点ID
            content: 新内容
            status: 新状态
            agent_name: 执行的agent名称

        Returns:
            是否更新成功
        """
        with self._lock:
            try:
                if node_id not in self.nodes:
                    self.logger.error(f"节点 {node_id} 不存在")
                    return False

                node = self.nodes[node_id]

                # 更新内容
                node.update_content(content, agent_name)

                # 更新状态
                if status:
                    node.status = status

                # 触发回调
                self._trigger_node_callbacks(node_id, 'content_updated', node)

                self.logger.debug(f"节点内容已更新: {node_id}")
                return True

            except Exception as e:
                self.logger.error(f"更新节点内容失败: {e}")
                return False

    def get_execution_order(self, use_cache: bool = True) -> List[str]:
        """
        获取节点的执行顺序（拓扑排序）

        Args:
            use_cache: 是否使用缓存

        Returns:
            节点ID的执行顺序列表
        """
        if use_cache and self._cache_valid and self._execution_order_cache:
            self.stats['cache_hits'] += 1
            return self._execution_order_cache.copy()

        self.stats['cache_misses'] += 1

        with self._lock:
            try:
                # 拓扑排序实现
                in_degree = {node_id: 0 for node_id in self.nodes}

                # 计算入度
                for from_id, to_id in self.edges:
                    if to_id in in_degree:
                        in_degree[to_id] += 1

                # 找出入度为0的节点
                queue = [node_id for node_id, degree in in_degree.items() if degree == 0]
                result = []

                while queue:
                    # 按优先级排序
                    queue.sort(key=lambda nid: self._get_node_priority_value(nid), reverse=True)

                    current = queue.pop(0)
                    result.append(current)

                    # 更新相邻节点的入度
                    for from_id, to_id in self.edges:
                        if from_id == current and to_id in in_degree:
                            in_degree[to_id] -= 1
                            if in_degree[to_id] == 0:
                                queue.append(to_id)

                # 检查是否有循环依赖
                if len(result) != len(self.nodes):
                    remaining_nodes = set(self.nodes.keys()) - set(result)
                    self.logger.error(f"检测到循环依赖，涉及节点: {remaining_nodes}")
                    # 将剩余节点按优先级添加到结果中
                    remaining_sorted = sorted(remaining_nodes,
                                              key=lambda nid: self._get_node_priority_value(nid),
                                              reverse=True)
                    result.extend(remaining_sorted)

                # 缓存结果
                self._execution_order_cache = result.copy()
                self._cache_valid = True

                return result

            except Exception as e:
                self.logger.error(f"获取执行顺序失败: {e}")
                return list(self.nodes.keys())

    def get_ready_nodes(self) -> List[str]:
        """
        获取当前可以执行的节点

        Returns:
            可执行节点ID列表
        """
        with self._lock:
            completed_nodes = {node_id for node_id, node in self.nodes.items()
                               if node.status == NodeStatus.COMPLETED}

            ready_nodes = []
            for node_id, node in self.nodes.items():
                if node.can_execute(completed_nodes):
                    ready_nodes.append(node_id)

            # 按优先级排序
            ready_nodes.sort(key=lambda nid: self._get_node_priority_value(nid), reverse=True)

            return ready_nodes

    def execute_node(self,
                     node_id: str,
                     executor_func: Callable,
                     agent_name: str = "",
                     async_execution: bool = False) -> Union[bool, Future]:
        """
        执行指定节点

        Args:
            node_id: 节点ID
            executor_func: 执行函数
            agent_name: 执行的agent名称
            async_execution: 是否异步执行

        Returns:
            同步执行返回bool，异步执行返回Future对象
        """
        if node_id not in self.nodes:
            self.logger.error(f"节点 {node_id} 不存在")
            return False

        node = self.nodes[node_id]

        def _execute():
            try:
                with self._lock:
                    node.start_execution(agent_name)
                    self._trigger_node_callbacks(node_id, 'execution_started', node)

                # 执行函数
                start_time = datetime.now()

                result = executor_func(node)

                execution_time = (datetime.now() - start_time).total_seconds()

                with self._lock:
                    # 更新指标
                    if self.enable_metrics:
                        node.metrics.execution_time = execution_time

                    # 完成执行
                    success = result.get('success', False) if isinstance(result, dict) else bool(result)
                    error_msg = result.get('error', '') if isinstance(result, dict) else ''

                    node.complete_execution(success, error_msg)

                    # 更新统计
                    self.stats['total_nodes_processed'] += 1
                    if success:
                        self.execution_state['completed_nodes'] += 1
                        self.stats['successful_executions'] += 1
                    else:
                        self.execution_state['failed_nodes'] += 1
                        self.stats['failed_executions'] += 1

                    # 触发回调
                    self._trigger_node_callbacks(node_id, 'execution_completed', node)

                return success

            except Exception as e:
                self.logger.error(f"节点 {node_id} 执行失败: {e}")

                with self._lock:
                    node.complete_execution(False, str(e))
                    self.execution_state['failed_nodes'] += 1
                    self.stats['failed_executions'] += 1

                    self._trigger_node_callbacks(node_id, 'execution_failed', node)

                return False

            finally:
                # 清理运行任务记录
                if node_id in self._running_tasks:
                    del self._running_tasks[node_id]

        if async_execution:
            future = self._executor.submit(_execute)
            self._running_tasks[node_id] = future
            return future
        else:
            return _execute()

    def execute_all(self,
                    executor_func: Callable,
                    parallel: bool = None) -> Dict[str, Any]:
        """
        执行所有节点

        Args:
            executor_func: 执行函数
            parallel: 是否并行执行，None表示根据execution_mode决定

        Returns:
            执行结果字典
        """
        start_time = datetime.now()

        try:
            self.execution_state['started_at'] = start_time.isoformat()
            self.execution_state['current_phase'] = 'executing'

            # 确定执行模式
            if parallel is None:
                use_parallel = self.execution_mode in [ExecutionMode.PARALLEL, ExecutionMode.ADAPTIVE]
            else:
                use_parallel = parallel

            if use_parallel:
                result = self._execute_parallel(executor_func)
            else:
                result = self._execute_sequential(executor_func)

            # 更新完成状态
            end_time = datetime.now()
            self.execution_state['completed_at'] = end_time.isoformat()
            self.execution_state['current_phase'] = 'completed'

            total_time = (end_time - start_time).total_seconds()

            # 更新统计
            self.stats['total_executions'] += 1
            current_avg = self.stats['average_execution_time']
            total_execs = self.stats['total_executions']
            self.stats['average_execution_time'] = (
                    (current_avg * (total_execs - 1) + total_time) / total_execs
            )

            result['execution_summary'] = {
                'total_time': total_time,
                'total_nodes': len(self.nodes),
                'completed_nodes': self.execution_state['completed_nodes'],
                'failed_nodes': self.execution_state['failed_nodes'],
                'execution_mode': use_parallel and 'parallel' or 'sequential'
            }

            self.logger.info(f"PGTree执行完成 - 用时: {total_time:.2f}秒")
            return result

        except Exception as e:
            self.logger.error(f"PGTree执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'execution_summary': self.get_execution_summary()
            }

    def _execute_sequential(self, executor_func: Callable) -> Dict[str, Any]:
        """顺序执行所有节点"""
        execution_order = self.get_execution_order()
        results = {}

        for node_id in execution_order:
            if node_id in self.nodes:
                self.logger.debug(f"执行节点: {node_id}")

                # 检查依赖是否满足
                ready_nodes = self.get_ready_nodes()
                if node_id not in ready_nodes:
                    self.logger.warning(f"节点 {node_id} 依赖未满足，跳过执行")
                    self.nodes[node_id].status = NodeStatus.BLOCKED
                    continue

                success = self.execute_node(node_id, executor_func)
                results[node_id] = success

                if not success:
                    self.logger.error(f"节点 {node_id} 执行失败")
                    # 根据策略决定是否继续
                    if self._should_stop_on_error(node_id):
                        break

        return {
            'success': self.execution_state['failed_nodes'] == 0,
            'results': results
        }

    def _execute_parallel(self, executor_func: Callable) -> Dict[str, Any]:
        """并行执行节点"""
        results = {}
        completed_nodes = set()

        while len(completed_nodes) < len(self.nodes):
            # 获取当前可执行的节点
            ready_nodes = self.get_ready_nodes()

            # 过滤已完成或正在执行的节点
            available_nodes = [
                node_id for node_id in ready_nodes
                if node_id not in completed_nodes and node_id not in self._running_tasks
            ]

            if not available_nodes:
                # 等待正在运行的任务完成
                if self._running_tasks:
                    # 等待至少一个任务完成
                    future = next(iter(self._running_tasks.values()))
                    future.result()  # 等待完成
                else:
                    # 没有可执行的节点且没有运行中的任务，可能存在问题
                    remaining_nodes = set(self.nodes.keys()) - completed_nodes
                    self.logger.warning(f"无法继续执行，剩余节点: {remaining_nodes}")
                    break
                continue

            # 限制并行数量
            max_parallel = min(self.max_parallel_nodes, len(available_nodes))
            nodes_to_execute = available_nodes[:max_parallel]

            # 启动并行执行
            for node_id in nodes_to_execute:
                self.logger.debug(f"并行执行节点: {node_id}")
                future = self.execute_node(node_id, executor_func, async_execution=True)
                self._running_tasks[node_id] = future

            # 等待一些任务完成
            while len(self._running_tasks) >= self.max_parallel_nodes:
                # 检查完成的任务
                finished_tasks = []
                for node_id, future in self._running_tasks.items():
                    if future.done():
                        try:
                            success = future.result()
                            results[node_id] = success
                            completed_nodes.add(node_id)
                            finished_tasks.append(node_id)

                            if not success and self._should_stop_on_error(node_id):
                                # 取消所有运行中的任务
                                for task_future in self._running_tasks.values():
                                    task_future.cancel()
                                return {
                                    'success': False,
                                    'results': results,
                                    'error': f'节点 {node_id} 执行失败，停止执行'
                                }
                        except Exception as e:
                            self.logger.error(f"获取节点 {node_id} 执行结果失败: {e}")
                            results[node_id] = False
                            completed_nodes.add(node_id)
                            finished_tasks.append(node_id)

                # 清理完成的任务
                for task_id in finished_tasks:
                    if task_id in self._running_tasks:
                        del self._running_tasks[task_id]

                if not finished_tasks:
                    # 如果没有任务完成，短暂等待
                    import time
                    time.sleep(0.1)

        # 等待所有剩余任务完成
        for node_id, future in self._running_tasks.items():
            try:
                success = future.result()
                results[node_id] = success
                completed_nodes.add(node_id)
            except Exception as e:
                self.logger.error(f"等待节点 {node_id} 完成失败: {e}")
                results[node_id] = False

        self._running_tasks.clear()

        return {
            'success': self.execution_state['failed_nodes'] == 0,
            'results': results
        }

    def _should_stop_on_error(self, failed_node_id: str) -> bool:
        """判断是否应该在错误时停止执行"""
        # 可以根据节点的重要性或配置来决定
        node = self.nodes.get(failed_node_id)
        if node and node.priority == 'critical':
            return True
        return False

    def _add_edge(self, from_node: str, to_node: str):
        """添加边"""
        edge = (from_node, to_node)
        if edge not in self.edges:
            self.edges.append(edge)

            # 更新children关系
            if from_node in self.nodes:
                if to_node not in self.nodes[from_node].children:
                    self.nodes[from_node].children.append(to_node)

    def _get_node_priority_value(self, node_id: str) -> int:
        """获取节点优先级数值"""
        if node_id not in self.nodes:
            return 0

        priority_map = {
            'critical': 4,
            'high': 3,
            'medium': 2,
            'low': 1
        }

        return priority_map.get(self.nodes[node_id].priority, 2)

    def _invalidate_cache(self):
        """使缓存失效"""
        self._cache_valid = False
        self._execution_order_cache = None
        self._dependency_cache.clear()

    def _trigger_node_callbacks(self, node_id: str, event: str, node: PGTreeNode):
        """触发节点回调"""
        try:
            # 节点特定回调
            if node_id in self._node_callbacks:
                for callback in self._node_callbacks[node_id]:
                    callback(event, node)

            # 全局回调
            for callback in self._global_callbacks:
                callback(event, node)

        except Exception as e:
            self.logger.error(f"触发回调失败: {e}")

    def add_node_callback(self, node_id: str, callback: Callable):
        """添加节点特定回调"""
        if node_id not in self._node_callbacks:
            self._node_callbacks[node_id] = []
        self._node_callbacks[node_id].append(callback)

    def add_global_callback(self, callback: Callable):
        """添加全局回调"""
        self._global_callbacks.append(callback)

    def get_node(self, node_id: str) -> Optional[PGTreeNode]:
        """获取节点"""
        return self.nodes.get(node_id)

    def get_nodes_by_status(self, status: NodeStatus) -> List[PGTreeNode]:
        """根据状态获取节点列表"""
        return [node for node in self.nodes.values() if node.status == status]

    def get_dependency_chain(self, node_id: str) -> List[str]:
        """获取节点的完整依赖链"""
        if node_id in self._dependency_cache:
            return self._dependency_cache[node_id].copy()

        visited = set()
        chain = []

        def _collect_dependencies(current_id):
            if current_id in visited or current_id not in self.nodes:
                return

            visited.add(current_id)
            node = self.nodes[current_id]

            for dep_id in node.dependencies:
                _collect_dependencies(dep_id)
                if dep_id not in chain:
                    chain.append(dep_id)

        _collect_dependencies(node_id)

        # 缓存结果
        self._dependency_cache[node_id] = chain.copy()

        return chain

    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        total_nodes = len(self.nodes)
        completed = sum(1 for node in self.nodes.values() if node.status == NodeStatus.COMPLETED)
        failed = sum(1 for node in self.nodes.values() if node.status == NodeStatus.ERROR)
        in_progress = sum(1 for node in self.nodes.values() if node.status == NodeStatus.IN_PROGRESS)

        return {
            'tree_id': self.tree_id,
            'total_nodes': total_nodes,
            'completed_nodes': completed,
            'failed_nodes': failed,
            'in_progress_nodes': in_progress,
            'completion_rate': completed / total_nodes if total_nodes > 0 else 0,
            'execution_state': self.execution_state.copy(),
            'statistics': self.stats.copy()
        }

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        if not self.enable_metrics:
            return {'error': '指标收集未启用'}

        node_metrics = {}
        total_execution_time = 0
        total_quality_score = 0
        quality_count = 0

        for node_id, node in self.nodes.items():
            metrics = node.metrics.to_dict()
            metrics['node_id'] = node_id
            metrics['status'] = node.status.value
            node_metrics[node_id] = metrics

            total_execution_time += node.metrics.execution_time
            if node.metrics.quality_score > 0:
                total_quality_score += node.metrics.quality_score
                quality_count += 1

        return {
            'node_metrics': node_metrics,
            'summary': {
                'total_execution_time': total_execution_time,
                'average_quality_score': total_quality_score / quality_count if quality_count > 0 else 0,
                'total_retries': sum(node.metrics.retry_count for node in self.nodes.values()),
                'cache_hit_rate': self.stats['cache_hits'] / (
                            self.stats['cache_hits'] + self.stats['cache_misses']) if (self.stats['cache_hits'] +
                                                                                       self.stats[
                                                                                           'cache_misses']) > 0 else 0
            }
        }

    def export_to_dict(self) -> Dict[str, Any]:
        """导出为字典格式"""
        return {
            'tree_id': self.tree_id,
            'execution_mode': self.execution_mode.value,
            'max_parallel_nodes': self.max_parallel_nodes,
            'nodes': {node_id: node.to_dict() for node_id, node in self.nodes.items()},
            'edges': self.edges,
            'execution_state': self.execution_state,
            'statistics': self.stats,
            'created_at': datetime.now().isoformat()
        }

    def import_from_dict(self, data: Dict[str, Any]) -> bool:
        """从字典导入数据"""
        try:
            with self._lock:
                self.tree_id = data.get('tree_id', self.tree_id)
                self.execution_mode = ExecutionMode(data.get('execution_mode', 'sequential'))
                self.max_parallel_nodes = data.get('max_parallel_nodes', 3)

                # 重建节点
                self.nodes.clear()
                nodes_data = data.get('nodes', {})

                for node_id, node_dict in nodes_data.items():
                    node = PGTreeNode(
                        node_id=node_dict['node_id'],
                        section_name=node_dict['section_name'],
                        node_type=NodeType(node_dict.get('node_type', 'section')),
                        status=NodeStatus(node_dict.get('status', 'pending')),
                        priority=node_dict.get('priority', 'medium'),
                        content=node_dict.get('content', ''),
                        dependencies=node_dict.get('dependencies', []),
                        children=node_dict.get('children', []),
                        metadata=node_dict.get('metadata', {}),
                        assigned_agent=node_dict.get('assigned_agent', ''),
                        word_count=node_dict.get('word_count', 0),
                        estimated_time=node_dict.get('estimated_time', 0),
                        requirements=node_dict.get('requirements', ''),
                        writing_guide=node_dict.get('writing_guide', ''),
                        version=node_dict.get('version', 1)
                    )

                    # 恢复指标
                    if 'metrics' in node_dict:
                        metrics_data = node_dict['metrics']
                        node.metrics = NodeMetrics(
                            execution_time=metrics_data.get('execution_time', 0.0),
                            retry_count=metrics_data.get('retry_count', 0),
                            memory_usage=metrics_data.get('memory_usage', 0.0),
                            cpu_usage=metrics_data.get('cpu_usage', 0.0),
                            quality_score=metrics_data.get('quality_score', 0.0),
                            complexity_score=metrics_data.get('complexity_score', 1.0)
                        )

                    self.nodes[node_id] = node

                # 重建边
                self.edges = data.get('edges', [])

                # 恢复执行状态
                self.execution_state.update(data.get('execution_state', {}))

                # 恢复统计
                self.stats.update(data.get('statistics', {}))

                # 清除缓存
                self._invalidate_cache()

                self.logger.info(f"PGTree数据导入成功 - {len(self.nodes)} 个节点")
                return True

        except Exception as e:
            self.logger.error(f"数据导入失败: {e}")
            return False

    def save_to_file(self, file_path: str) -> bool:
        """保存到文件"""
        try:
            data = self.export_to_dict()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"PGTree已保存到: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"保存文件失败: {e}")
            return False

    def load_from_file(self, file_path: str) -> bool:
        """从文件加载"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            success = self.import_from_dict(data)
            if success:
                self.logger.info(f"PGTree已从文件加载: {file_path}")

            return success

        except Exception as e:
            self.logger.error(f"加载文件失败: {e}")
            return False

    def reset(self):
        """重置PGTree"""
        with self._lock:
            # 取消所有运行中的任务
            for future in self._running_tasks.values():
                future.cancel()
            self._running_tasks.clear()

            # 重置数据
            self.nodes.clear()
            self.edges.clear()

            # 重置状态
            self.execution_state = {
                'started_at': None,
                'completed_at': None,
                'total_nodes': 0,
                'completed_nodes': 0,
                'failed_nodes': 0,
                'current_phase': 'initialization'
            }

            # 清除缓存
            self._invalidate_cache()

            self.logger.info("PGTree已重置")

    def cleanup(self):
        """清理资源"""
        # 关闭线程池
        self._executor.shutdown(wait=True)

        # 清理回调
        self._node_callbacks.clear()
        self._global_callbacks.clear()

        self.logger.info("PGTree资源已清理")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()

    def __len__(self) -> int:
        """返回节点数量"""
        return len(self.nodes)

    def __contains__(self, node_id: str) -> bool:
        """检查节点是否存在"""
        return node_id in self.nodes

    def __iter__(self):
        """迭代节点"""
        return iter(self.nodes.values())

    def __repr__(self) -> str:
        """字符串表示"""
        return (f"PGTreeHandler(tree_id='{self.tree_id}', "
                f"nodes={len(self.nodes)}, "
                f"mode={self.execution_mode.value})")