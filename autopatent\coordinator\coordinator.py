"""
主协调器 - 管理AutoPatent多智能体工作流程
"""

import logging
from typing import Dict, Any, List, Optional
from enum import Enum
import json
from datetime import datetime
import asyncio

from ..agents.planner_agent import PlannerAgent, PGTree
from ..agents.writer_agent import WriterAgent
from ..agents.examiner_agent import ExaminerAgent
from ..database.patent_db import PatentDB
from ..utils.token_tracker import TokenTracker


class WorkflowState(Enum):
    """工作流状态枚举"""
    INITIALIZED = "initialized"  # 已初始化
    PLANNING = "planning"  # 规划阶段
    WRITING = "writing"  # 写作阶段
    EXAMINING = "examining"  # 审查阶段
    REVISING = "revising"  # 修订阶段
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败


class AutoPatentCoordinator:
    """AutoPatent多智能体框架的主协调器"""

    def __init__(self,
                 db_path: str = "patents.db",
                 max_iterations: int = 3,
                 quality_threshold: float = 7.0,
                 enable_parallel_processing: bool = False):
        """
        初始化AutoPatent协调器

        Args:
            db_path: 专利数据库路径
            max_iterations: 最大迭代次数
            quality_threshold: 质量阈值评分
            enable_parallel_processing: 是否启用并行处理
        """
        self.db_path = db_path
        self.max_iterations = max_iterations
        self.quality_threshold = quality_threshold
        self.enable_parallel_processing = enable_parallel_processing

        # 初始化组件
        self.patent_db = PatentDB(db_path)
        self.planner_agent = PlannerAgent()
        self.writer_agents = {
            'general': WriterAgent('general'),
            'technical': WriterAgent('technical'),
            'legal': WriterAgent('legal')
        }
        self.examiner_agent = ExaminerAgent(self.patent_db)

        # 工作流管理
        self.current_state = WorkflowState.INITIALIZED
        self.workflow_history = []
        self.token_tracker = TokenTracker()
        self.logger = logging.getLogger("AutoPatent.Coordinator")

        # 当前workflow数据
        self.current_pgtree = None
        self.current_concept = None
        self.current_workflow_id = None
        self.iteration_count = 0
        self.start_time = None

        # 性能监控
        self.performance_metrics = {
            "total_processing_time": 0.0,
            "planning_time": 0.0,
            "writing_time": 0.0,
            "examining_time": 0.0,
            "revision_time": 0.0
        }

        self.logger.info("AutoPatent协调器初始化完成")

    def generate_patent(self, concept: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        主方法：从初始concept生成完整专利

        Args:
            concept: 初始专利概念或草稿
            options: 生成选项配置

        Returns:
            包含生成专利和workflow结果的字典
        """
        if not options:
            options = {}

        # 初始化workflow
        self.current_workflow_id = f"workflow_{int(datetime.now().timestamp())}"
        self.start_time = datetime.now()
        self.current_concept = concept
        self.iteration_count = 0

        self.logger.info(f"开始专利生成workflow: {self.current_workflow_id}")
        self.logger.info(f"概念预览: {concept[:100]}...")

        try:
            # Phase 1: 规划阶段
            planning_result = self._execute_planning_phase(concept, options)
            if not planning_result.get('success'):
                return self._create_error_result("规划阶段失败", planning_result)

            # Phase 2: 写作阶段
            writing_result = self._execute_writing_phase(options)
            if not writing_result.get('success'):
                return self._create_error_result("写作阶段失败", writing_result)

            # Phase 3: 审查和修订循环
            examination_result = self._execute_examination_phase(options)
            if not examination_result.get('success'):
                return self._create_error_result("审查阶段失败", examination_result)

            # 生成最终结果
            return self._create_success_result()

        except Exception as e:
            self.logger.error(f"专利生成失败: {e}")
            self.current_state = WorkflowState.FAILED
            return self._create_error_result(f"系统异常: {e}")

    async def generate_patent_async(self, concept: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        异步版本的专利生成方法

        Args:
            concept: 初始专利概念
            options: 生成选项

        Returns:
            生成结果
        """
        if not self.enable_parallel_processing:
            # 如果未启用并行处理，使用同步版本
            return self.generate_patent(concept, options)

        self.logger.info("使用异步并行模式生成专利")

        # 这里可以实现真正的异步处理逻辑
        # 目前先包装同步方法
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generate_patent, concept, options)

    def _execute_planning_phase(self, concept: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """执行规划阶段，使用PlannerAgent生成PGTree"""
        self.logger.info("开始执行规划阶段...")
        self.current_state = WorkflowState.PLANNING

        phase_start_time = datetime.now()

        try:
            # 创建规划使用PlannerAgent
            planning_input = {
                'concept': concept,
                'options': options,
                'workflow_id': self.current_workflow_id
            }

            self.logger.debug("调用PlannerAgent进行规划...")
            planning_result = self.planner_agent.process(planning_input)

            if 'error' in planning_result:
                self.logger.error(f"PlannerAgent返回错误: {planning_result['error']}")
                return {'success': False, 'error': planning_result['error']}

            self.current_pgtree = planning_result['pgtree']

            # 记录规划阶段完成
            phase_duration = (datetime.now() - phase_start_time).total_seconds()
            self.performance_metrics["planning_time"] = phase_duration

            self.workflow_history.append({
                'phase': 'planning',
                'timestamp': datetime.now().isoformat(),
                'duration_seconds': phase_duration,
                'result': 'success',
                'details': planning_result.get('plan_details', {}),
                'pgtree_nodes': len(self.current_pgtree.structure) if self.current_pgtree else 0
            })

            self.logger.info(f"规划阶段完成，耗时 {phase_duration:.2f}秒")
            return {'success': True, 'pgtree': self.current_pgtree, 'duration': phase_duration}

        except Exception as e:
            self.logger.error(f"规划阶段异常: {e}")
            return {'success': False, 'error': str(e)}

    def _execute_writing_phase(self, options: Dict[str, Any]) -> Dict[str, Any]:
        """执行写作阶段，使用WriterAgent生成内容"""
        self.logger.info("开始执行写作阶段...")
        self.current_state = WorkflowState.WRITING

        phase_start_time = datetime.now()

        try:
            # 定义section写作顺序（基于依赖关系）
            writing_order = self._determine_writing_order()

            writing_results = {}
            total_sections = len(writing_order)

            for i, section in enumerate(writing_order, 1):
                self.logger.info(f"写作section: {section} ({i}/{total_sections})")

                # 选择合适的writer agent
                writer_agent = self._select_writer_agent(section)

                # 准备写作context
                writing_context = self._prepare_writing_context(section, options)

                # 执行写作
                section_start_time = datetime.now()
                writing_input = {
                    'pgtree': self.current_pgtree,
                    'section': section,
                    'context': writing_context,
                    'workflow_id': self.current_workflow_id
                }

                writing_result = writer_agent.process(writing_input)
                section_duration = (datetime.now() - section_start_time).total_seconds()

                if 'error' in writing_result:
                    self.logger.error(f"写作section {section} 失败: {writing_result['error']}")
                    return {'success': False, 'error': f"写作{section}失败: {writing_result['error']}"}

                writing_results[section] = {
                    'result': writing_result,
                    'duration': section_duration,
                    'word_count': len(writing_result.get('content', '').split()),
                    'agent_used': writer_agent.agent_name
                }

                # 更新PGTree（由writer agent处理）
                self.current_pgtree = writing_result['pgtree']

                self.logger.debug(f"Section {section} 完成，耗时 {section_duration:.2f}秒")

            # 记录写作阶段完成
            phase_duration = (datetime.now() - phase_start_time).total_seconds()
            self.performance_metrics["writing_time"] = phase_duration

            total_words = sum(result['word_count'] for result in writing_results.values())

            self.workflow_history.append({
                'phase': 'writing',
                'timestamp': datetime.now().isoformat(),
                'duration_seconds': phase_duration,
                'result': 'success',
                'sections_completed': list(writing_results.keys()),
                'total_words': total_words,
                'average_words_per_section': total_words / len(writing_results) if writing_results else 0
            })

            self.logger.info(f"写作阶段完成，总耗时 {phase_duration:.2f}秒，生成 {total_words} 词")
            return {
                'success': True,
                'writing_results': writing_results,
                'duration': phase_duration,
                'total_words': total_words
            }

        except Exception as e:
            self.logger.error(f"写作阶段异常: {e}")
            return {'success': False, 'error': str(e)}

    def _execute_examination_phase(self, options: Dict[str, Any]) -> Dict[str, Any]:
        """执行审查阶段，包含修订循环"""
        self.logger.info("开始执行审查阶段...")
        self.current_state = WorkflowState.EXAMINING

        phase_start_time = datetime.now()
        examination_results = []

        try:
            for iteration in range(self.max_iterations):
                self.iteration_count = iteration + 1
                self.logger.info(f"开始第 {self.iteration_count} 轮审查")

                iteration_start_time = datetime.now()

                # 审查当前专利内容
                examination_input = {
                    'pgtree': self.current_pgtree,
                    'section': 'all',  # 审查所有section
                    'workflow_id': self.current_workflow_id,
                    'iteration': self.iteration_count
                }

                examination_result = self.examiner_agent.process(examination_input)
                iteration_duration = (datetime.now() - iteration_start_time).total_seconds()

                if 'error' in examination_result:
                    self.logger.error(f"审查失败: {examination_result['error']}")
                    return {'success': False, 'error': f"审查失败: {examination_result['error']}"}

                examination_result['iteration'] = self.iteration_count
                examination_result['duration'] = iteration_duration
                examination_results.append(examination_result)

                # 检查质量是否达到阈值
                overall_score = examination_result.get('overall_score', 0)
                self.logger.info(f"第 {self.iteration_count} 轮审查完成，质量评分: {overall_score:.2f}")

                if overall_score >= self.quality_threshold:
                    self.logger.info(f"质量达到阈值 {self.quality_threshold}，审查通过")
                    break

                # 如果不是最后一次迭代，执行修订
                if iteration < self.max_iterations - 1:
                    self.logger.info("质量未达标，开始修订...")
                    revision_result = self._execute_revision_phase(examination_result)

                    if not revision_result.get('success'):
                        self.logger.warning("修订失败，继续使用当前版本")
                    else:
                        self.logger.info("修订完成")
                else:
                    self.logger.warning(f"已达到最大迭代次数 {self.max_iterations}，停止修订")

            # 记录审查阶段完成
            phase_duration = (datetime.now() - phase_start_time).total_seconds()
            self.performance_metrics["examining_time"] = phase_duration

            final_score = examination_results[-1].get('overall_score', 0) if examination_results else 0

            self.workflow_history.append({
                'phase': 'examination',
                'timestamp': datetime.now().isoformat(),
                'duration_seconds': phase_duration,
                'result': 'success',
                'iterations': len(examination_results),
                'final_score': final_score,
                'quality_threshold': self.quality_threshold,
                'threshold_met': final_score >= self.quality_threshold
            })

            self.logger.info(f"审查阶段完成，共 {len(examination_results)} 轮，最终评分: {final_score:.2f}")
            return {
                'success': True,
                'examination_results': examination_results,
                'final_score': final_score,
                'iterations': len(examination_results)
            }

        except Exception as e:
            self.logger.error(f"审查阶段异常: {e}")
            return {'success': False, 'error': str(e)}

    def _execute_revision_phase(self, examination_result: Dict[str, Any]) -> Dict[str, Any]:
        """执行修订阶段，基于审查反馈"""
        self.logger.info("开始执行修订阶段...")
        self.current_state = WorkflowState.REVISING

        phase_start_time = datetime.now()

        try:
            recommendations = examination_result.get('recommendations', [])

            if not recommendations:
                self.logger.info("没有修订建议，跳过修订")
                return {'success': True, 'message': '无需修订'}

            revision_results = {}

            for i, recommendation in enumerate(recommendations, 1):
                self.logger.debug(f"处理修订建议 {i}/{len(recommendations)}: {recommendation[:100]}...")

                # 解析recommendation确定section和feedback
                section, feedback = self._parse_recommendation(recommendation)

                if section and section in self.current_pgtree.structure:
                    # 获取适当的writer agent
                    writer_agent = self._select_writer_agent(section)

                    # 获取当前content
                    current_content = self.current_pgtree.structure[section].get('content', '')

                    if not current_content:
                        self.logger.warning(f"Section {section} 没有现有content，跳过修订")
                        continue

                    # 执行content修订
                    self.logger.debug(f"修订section: {section}")
                    revised_content = writer_agent.revise_content(current_content, feedback, section)

                    # 更新PGTree
                    self.current_pgtree.update_node(section, revised_content)

                    revision_results[section] = {
                        'original_length': len(current_content),
                        'revised_length': len(revised_content),
                        'feedback': feedback,
                        'agent_used': writer_agent.agent_name
                    }

                    self.logger.debug(f"Section {section} 修订完成")

            # 记录修订阶段完成
            phase_duration = (datetime.now() - phase_start_time).total_seconds()
            self.performance_metrics["revision_time"] += phase_duration

            self.workflow_history.append({
                'phase': 'revision',
                'timestamp': datetime.now().isoformat(),
                'duration_seconds': phase_duration,
                'result': 'success',
                'sections_revised': list(revision_results.keys()),
                'recommendations_processed': len(recommendations)
            })

            self.logger.info(f"修订阶段完成，修订了 {len(revision_results)} 个section，耗时 {phase_duration:.2f}秒")
            return {'success': True, 'revision_results': revision_results}

        except Exception as e:
            self.logger.error(f"修订阶段异常: {e}")
            return {'success': False, 'error': str(e)}

    def _determine_writing_order(self) -> List[str]:
        """确定section写作顺序（基于依赖关系）"""
        if not self.current_pgtree:
            return []

        # 简化的依赖排序逻辑
        # 在实际实现中，这里会分析PGTree的依赖关系
        default_order = ['background', 'summary', 'detailed_description', 'claims', 'abstract']

        # 只返回PGTree中实际存在的section
        available_sections = list(self.current_pgtree.structure.keys())
        ordered_sections = [section for section in default_order if section in available_sections]

        # 添加任何不在默认顺序中的section
        for section in available_sections:
            if section not in ordered_sections:
                ordered_sections.append(section)

        return ordered_sections

    def _select_writer_agent(self, section: str) -> WriterAgent:
        """为特定section选择合适的writer agent"""
        if section in ['claims']:
            return self.writer_agents['legal']
        elif section in ['detailed_description', 'background']:
            return self.writer_agents['technical']
        else:
            return self.writer_agents['general']

    def _prepare_writing_context(self, section: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """为特定section准备写作context"""
        context = {
            'concept': self.current_concept,
            'section': section,
            'workflow_id': self.current_workflow_id,
            'options': options
        }

        # 添加已完成section的信息作为context
        if hasattr(self.current_pgtree, 'structure'):
            for completed_section, data in self.current_pgtree.structure.items():
                if data.get('status') == 'completed' and data.get('content'):
                    context[f'{completed_section}_content'] = data['content'][:300]  # 摘要版本

        return context

    def _parse_recommendation(self, recommendation: str) -> tuple:
        """解析审查recommendation，提取section和feedback"""
        recommendation_lower = recommendation.lower()

        # 预定义的section列表
        sections = ['abstract', 'background', 'summary', 'detailed_description', 'claims']

        # 查找recommendation中提到的section
        for section in sections:
            if section in recommendation_lower:
                return section, recommendation

        # 如果没有找到特定section，返回通用处理
        return 'general', recommendation

    def _create_success_result(self) -> Dict[str, Any]:
        """创建成功的专利生成结果"""
        self.current_state = WorkflowState.COMPLETED
        end_time = datetime.now()

        # 从PGTree提取专利content
        patent_content = {}
        for section, data in self.current_pgtree.structure.items():
            if data.get('content'):
                patent_content[section] = data['content']

        # 计算总处理时间
        total_duration = (end_time - self.start_time).total_seconds()
        self.performance_metrics["total_processing_time"] = total_duration

        # 收集token使用统计
        token_usage = self._collect_token_usage()

        result = {
            'success': True,
            'workflow_id': self.current_workflow_id,
            'patent_content': patent_content,
            'pgtree': self.current_pgtree,
            'workflow_state': self.current_state.value,
            'workflow_history': self.workflow_history,
            'performance_metrics': self.performance_metrics,
            'statistics': {
                'iterations': self.iteration_count,
                'total_phases': len(self.workflow_history),
                'start_time': self.start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'total_duration_seconds': total_duration,
                'sections_generated': len(patent_content),
                'total_words': sum(len(content.split()) for content in patent_content.values())
            },
            'token_usage': token_usage
        }

        self.logger.info(f"专利生成成功完成，workflow_id: {self.current_workflow_id}")
        self.logger.info(f"总耗时: {total_duration:.2f}秒，迭代次数: {self.iteration_count}")

        return result

    def _create_error_result(self, error_message: str, details: Dict = None) -> Dict[str, Any]:
        """创建失败的专利生成结果"""
        self.current_state = WorkflowState.FAILED
        end_time = datetime.now()

        if self.start_time:
            duration = (end_time - self.start_time).total_seconds()
        else:
            duration = 0

        result = {
            'success': False,
            'workflow_id': self.current_workflow_id,
            'error': error_message,
            'workflow_state': self.current_state.value,
            'workflow_history': self.workflow_history,
            'performance_metrics': self.performance_metrics,
            'statistics': {
                'duration_seconds': duration,
                'failed_at': end_time.isoformat()
            },
            'details': details or {}
        }

        self.logger.error(f"专利生成失败: {error_message}")
        return result

    def _collect_token_usage(self) -> Dict[str, Any]:
        """收集所有agent的token使用情况"""
        usage = {
            'planner': self.planner_agent.get_token_usage(),
            'writers': {name: agent.get_token_usage()
                        for name, agent in self.writer_agents.items()},
            'examiner': self.examiner_agent.get_token_usage()
        }

        # 计算总计
        total_input = 0
        total_output = 0

        # planner使用量
        planner_usage = usage['planner']
        total_input += planner_usage.get('input', 0)
        total_output += planner_usage.get('output', 0)

        # writers使用量
        for writer_usage in usage['writers'].values():
            total_input += writer_usage.get('input', 0)
            total_output += writer_usage.get('output', 0)

        # examiner使用量
        examiner_usage = usage['examiner']
        total_input += examiner_usage.get('input', 0)
        total_output += examiner_usage.get('output', 0)

        usage['total'] = {
            'input_tokens': total_input,
            'output_tokens': total_output,
            'total_tokens': total_input + total_output
        }

        return usage

    def get_workflow_status(self) -> Dict[str, Any]:
        """获取当前workflow状态"""
        if not self.current_workflow_id:
            return {"message": "没有活跃的workflow"}

        status = {
            "workflow_id": self.current_workflow_id,
            "current_state": self.current_state.value,
            "iteration_count": self.iteration_count,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "elapsed_time": (datetime.now() - self.start_time).total_seconds() if self.start_time else 0,
            "performance_metrics": self.performance_metrics
        }

        return status

    def save_patent(self, patent_id: str) -> bool:
        """保存生成的专利到database"""
        if not self.current_pgtree or self.current_state != WorkflowState.COMPLETED:
            self.logger.error("没有完成的专利可以保存")
            return False

        try:
            patent_data = {
                'patent_id': patent_id,
                'title': self._extract_title(),
                'abstract': self.current_pgtree.structure.get('abstract', {}).get('content', ''),
                'claims': self.current_pgtree.structure.get('claims', {}).get('content', ''),
                'description': self._combine_description_sections(),
                'technical_field': self._extract_technical_field(),
                'filing_date': datetime.now().isoformat(),
                'workflow_id': self.current_workflow_id,
                'generation_metadata': {
                    'iterations': self.iteration_count,
                    'total_duration': self.performance_metrics["total_processing_time"],
                    'workflow_history': self.workflow_history
                }
            }

            success = self.patent_db.add_patent(patent_data)

            if success:
                self.logger.info(f"专利已保存到database，patent_id: {patent_id}")
            else:
                self.logger.error(f"专利保存失败，patent_id: {patent_id}")

            return success

        except Exception as e:
            self.logger.error(f"保存专利时发生异常: {e}")
            return False

    def _extract_title(self) -> str:
        """从专利content中提取title"""
        # 尝试从summary或abstract中提取title
        summary_content = self.current_pgtree.structure.get('summary', {}).get('content', '')
        if summary_content:
            # 提取第一行有意义的内容作为title
            lines = summary_content.split('\n')
            for line in lines:
                clean_line = line.strip()
                if len(clean_line) > 10 and not clean_line.startswith('##'):
                    return clean_line[:100]  # 限制长度

        # 如果没有找到合适的title，使用concept的一部分
        if self.current_concept:
            return f"基于{self.current_concept[:50]}的发明"

        return f"AutoPatent生成的专利 - {self.current_workflow_id}"

    def _combine_description_sections(self) -> str:
        """合并所有description section"""
        sections_order = ['background', 'summary', 'detailed_description']
        combined_parts = []

        for section in sections_order:
            content = self.current_pgtree.structure.get(section, {}).get('content', '')
            if content:
                section_title = section.replace('_', ' ').title()
                combined_parts.append(f"## {section_title}\n\n{content}")

        return '\n\n'.join(combined_parts)

    def _extract_technical_field(self) -> str:
        """从专利content中提取技术领域"""
        background_content = self.current_pgtree.structure.get('background', {}).get('content', '')
        if background_content:
            # 提取第一段作为技术领域
            paragraphs = background_content.split('\n\n')
            if paragraphs:
                return paragraphs[0][:200]  # 限制长度

        return "通用技术领域"

    def reset_workflow(self):
        """重置workflow状态，准备新的生成任务"""
        self.current_state = WorkflowState.INITIALIZED
        self.workflow_history.clear()
        self.current_pgtree = None
        self.current_concept = None
        self.current_workflow_id = None
        self.iteration_count = 0
        self.start_time = None

        # 重置性能指标
        for key in self.performance_metrics:
            self.performance_metrics[key] = 0.0

        # 重置token tracker
        self.token_tracker.reset_session()

        self.logger.info("Workflow状态已重置")

    def export_workflow_report(self,
                               filepath: str = None,
                               include_pgtree: bool = True,
                               include_token_usage: bool = True) -> str:
        """
        导出workflow报告

        Args:
            filepath: 输出文件路径
            include_pgtree: 是否包含PGTree详情
            include_token_usage: 是否包含token使用详情

        Returns:
            导出文件路径
        """
        if not self.current_workflow_id:
            raise ValueError("没有活跃的workflow可以导出")

        if not filepath:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"workflow_report_{self.current_workflow_id}_{timestamp}.json"

        report_data = {
            "workflow_info": {
                "workflow_id": self.current_workflow_id,
                "state": self.current_state.value,
                "concept": self.current_concept,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "export_time": datetime.now().isoformat()
            },
            "workflow_history": self.workflow_history,
            "performance_metrics": self.performance_metrics,
            "statistics": {
                "total_iterations": self.iteration_count,
                "total_phases": len(self.workflow_history)
            }
        }

        if include_pgtree and self.current_pgtree:
            report_data["pgtree_info"] = {
                "completion_status": self.current_pgtree.get_completion_status(),
                "structure": self.current_pgtree.structure
            }

        if include_token_usage:
            report_data["token_usage"] = self._collect_token_usage()

        # 保存报告
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

        self.logger.info(f"Workflow报告已导出: {filepath}")
        return filepath