"""
ExaminerAgent 完整使用示例
"""

from draftPatents.agents.examiner_agent import ExaminerAgent, ExaminationType
from draftPatents.database.patent_db import PatentDB
from draftPatents.utils.pg_tree_handler import PGTreeHandler, PGTreeNode, NodeStatus
from draftPatents.utils.token_tracker import TokenTracker


def create_sample_pgtree():
    """创建示例PGTree用于测试"""
    # 创建PGTree处理器
    pg_tree = PGTreeHandler()

    # 添加示例节点
    nodes_data = {
        'abstract': {
            'section_name': 'abstract',
            'content': '''
            本发明涉及智能家居技术领域，特别是一种基于机器学习的智能能耗管理系统。
            该系统通过分析用户行为模式和设备使用习惯，自动优化家庭用电策略。
            系统包括数据采集模块、机器学习分析模块和智能控制模块。
            有益效果：能够显著降低家庭能耗，提高用电效率30%以上。
            ''',
            'status': NodeStatus.COMPLETED,
            'priority': 'high'
        },
        'background': {
            'section_name': 'background',
            'content': '''
            ## 技术领域
            本发明涉及智能家居技术领域，特别涉及家庭能耗管理技术。

            ## 背景技术
            随着智能家居设备的普及，家庭用电设备数量急剧增加。现有技术中，
            家庭能耗管理主要依靠用户手动控制或简单的定时器控制。

            现有技术存在以下问题：
            1. 无法根据用户实际需求智能调节
            2. 缺乏学习能力，不能适应用户习惯变化
            3. 能耗优化效果有限
            ''',
            'status': NodeStatus.COMPLETED,
            'priority': 'high'
        },
        'summary': {
            'section_name': 'summary',
            'content': '''
            ## 发明内容
            针对现有技术的不足，本发明的目的是提供一种智能能耗管理系统，
            能够自动学习用户行为模式，实现智能化的能耗优化。

            为实现上述目的，本发明采用如下技术方案：
            一种智能能耗管理系统，包括：
            - 数据采集模块：收集家庭用电设备的实时功耗数据
            - 机器学习分析模块：分析用户行为模式和设备使用规律
            - 智能控制模块：根据分析结果自动调节设备运行状态

            有益效果：
            1. 能够自动学习和适应用户习惯
            2. 显著降低家庭能耗，节能效果显著
            3. 提升用户使用体验
            ''',
            'status': NodeStatus.COMPLETED,
            'priority': 'high'
        },
        'claims': {
            'section_name': 'claims',
            'content': '''
            权利要求书

            1. 一种智能能耗管理系统，其特征在于，包括：
               数据采集模块，用于收集家庭用电设备的实时功耗数据；
               机器学习分析模块，用于分析用户行为模式和设备使用规律；
               智能控制模块，用于根据分析结果自动调节设备运行状态。

            2. 根据权利要求1所述的智能能耗管理系统，其特征在于：
               所述机器学习分析模块采用深度学习算法进行用户行为预测。

            3. 根据权利要求1所述的智能能耗管理系统，其特征在于：
               所述智能控制模块具有实时调节和预设调节两种工作模式。
            ''',
            'status': NodeStatus.COMPLETED,
            'priority': 'critical'
        }
    }

    # 创建节点并添加到PGTree
    for node_id, node_data in nodes_data.items():
        node = PGTreeNode(
            node_id=node_id,
            section_name=node_data['section_name'],
            content=node_data['content'],
            status=node_data['status'],
            priority=node_data['priority']
        )
        pg_tree.add_node(node)

    return pg_tree


def create_sample_database():
    """创建示例数据库并添加一些现有专利"""
    db_path = "example_patents.db"

    with PatentDB(db_path) as patent_db:
        # 添加一些示例现有专利
        sample_patents = [
            {
                'patent_id': 'CN101234567A',
                'title': '一种家庭智能用电管理装置',
                'abstract': '本发明提供一种家庭智能用电管理装置，通过监控家电设备用电情况，实现用电优化控制。',
                'technical_field': '智能家居',
                'quality_score': 7.5,
                'filing_date': '2023-01-15T00:00:00'
            },
            {
                'patent_id': 'CN102345678A',
                'title': '基于物联网的智能能耗监控系统',
                'abstract': '一种基于物联网技术的智能能耗监控系统，能够实时监控和分析家庭能耗数据。',
                'technical_field': '物联网技术',
                'quality_score': 8.2,
                'filing_date': '2023-03-20T00:00:00'
            },
            {
                'patent_id': 'CN103456789A',
                'title': '机器学习驱动的智能家居控制方法',
                'abstract': '采用机器学习算法分析用户行为，实现智能家居设备的自动控制和优化。',
                'technical_field': '人工智能',
                'quality_score': 8.8,
                'filing_date': '2023-05-10T00:00:00'
            }
        ]

        for patent_data in sample_patents:
            success = patent_db.add_patent(patent_data)
            if success:
                print(f"添加示例专利: {patent_data['patent_id']}")

    return db_path


def main():
    """主函数 - 完整的ExaminerAgent使用示例"""
    print("=== ExaminerAgent 使用示例 ===")

    # 1. 创建示例数据库
    print("\n1. 创建示例数据库...")
    db_path = create_sample_database()

    # 2. 创建示例PGTree
    print("\n2. 创建示例PGTree...")
    pgtree = create_sample_pgtree()
    print(f"PGTree创建完成，包含 {len(pgtree.nodes)} 个节点")

    # 3. 初始化ExaminerAgent
    print("\n3. 初始化ExaminerAgent...")
    with PatentDB(db_path) as patent_db:
        examiner = ExaminerAgent(
            patent_db=patent_db,
            examination_standards={
                'novelty_threshold': 0.7,
                'inventiveness_threshold': 0.6,
                'similarity_threshold': 0.8,
                'quality_threshold': 7.0,
                'min_word_count': 100,
                'max_similarity_allowed': 0.9
            },
            enable_rrag=True
        )

        print(f"ExaminerAgent初始化完成 - RRAG: 启用")

        # 4. 执行全面审查
        print("\n4. 执行全面审查...")
        input_data = {
            'pgtree': pgtree,
            'section': 'all',  # 审查所有section
            'examination_type': ExaminationType.SUBSTANTIVE.value,
            'workflow_id': 'example_workflow_001',
            'iteration': 1
        }

        result = examiner.process(input_data)

        # 5. 显示审查结果
        print("\n5. 审查结果:")
        if result['success']:
            print(f"✅ 审查成功完成")
            print(f"📊 总体评分: {result['overall_score']:.2f}/10")
            print(f"🔍 发现问题: {len(result['issues'])} 个")
            print(f"⏱️  处理时间: {result['examination_metadata']['processing_time']:.2f}秒")
            print(f"🎯 质量达标: {'是' if result['pass_criteria'] else '否'}")

            # 显示发现的问题
            if result['issues']:
                print(f"\n📋 发现的问题:")
                for i, issue in enumerate(result['issues'][:5], 1):  # 显示前5个问题
                    print(f"  {i}. [{issue['issue_type'].upper()}] {issue['description']}")
                    if issue['suggestion']:
                        print(f"     💡 建议: {issue['suggestion']}")

                if len(result['issues']) > 5:
                    print(f"     ... 还有 {len(result['issues']) - 5} 个问题")

            # 显示RRAG结果
            if result['rrag_result']:
                rrag = result['rrag_result']
                print(f"\n🔬 RRAG检索结果:")
                print(f"  📚 找到相似专利: {len(rrag['retrieved_patents'])} 个")
                print(f"  🆕 新颖性评分: {rrag['novelty_analysis']['novelty_score']:.2f}/10")
                print(f"  ⚠️  风险评估: {rrag['risk_assessment']}")

                if rrag['retrieved_patents']:
                    print(f"  📄 最相似专利:")
                    for patent in rrag['retrieved_patents'][:3]:
                        similarity = patent.get('similarity_score', 0)
                        print(f"    • {patent['patent_id']}: {patent['title'][:50]}... (相似度: {similarity:.3f})")

            # 显示审查建议
            if result['recommendations']:
                print(f"\n💭 审查建议:")
                for i, rec in enumerate(result['recommendations'][:5], 1):
                    print(f"  {i}. {rec}")
        else:
            print(f"❌ 审查失败: {result['error']}")

        # 6. 执行单个section审查示例
        print("\n6. 执行Claims审查...")
        claims_input = {
            'pgtree': pgtree,
            'section': 'claims',
            'examination_type': ExaminationType.SUBSTANTIVE.value,
            'workflow_id': 'example_workflow_001'
        }

        claims_result = examiner.process(claims_input)
        if claims_result['success']:
            print(f"   Claims评分: {claims_result['overall_score']:.2f}/10")
            print(f"   发现问题: {len(claims_result['issues'])} 个")

        # 7. 获取统计信息
        print("\n7. 获取审查统计:")
        stats = examiner.get_examination_statistics()
        print(f"  总审查次数: {stats['total_examinations']}")
        print(f"  RRAG搜索次数: {stats['rrag_searches_performed']}")
        print(f"  平均审查时间: {stats['average_examination_time']:.2f}秒")
        print(f"  平均质量评分: {stats['average_quality_score']:.2f}")

        # 8. Token使用统计
        print("\n8. Token使用统计:")
        token_usage = examiner.get_token_usage()
        print(f"  总Token数: {token_usage['total_tokens']:,}")
        print(f"  输入Token: {token_usage['total_input_tokens']:,}")
        print(f"  输出Token: {token_usage['total_output_tokens']:,}")
        print(f"  总成本: ${token_usage['total_cost']:.4f}")


if __name__ == "__main__":
    main()