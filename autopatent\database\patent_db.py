"""
专利数据库管理器 - 处理专利数据的存储、检索和管理
"""

import sqlite3
import json
import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import threading
from contextlib import contextmanager
import hashlib
import re
from dataclasses import dataclass


@dataclass
class SearchResult:
    """搜索结果数据类"""
    patent_id: str
    title: str
    abstract: str
    technical_field: str
    relevance_score: float
    similarity_score: Optional[float] = None
    matched_keywords: List[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'patent_id': self.patent_id,
            'title': self.title,
            'abstract': self.abstract,
            'technical_field': self.technical_field,
            'relevance_score': self.relevance_score,
            'similarity_score': self.similarity_score,
            'matched_keywords': self.matched_keywords or []
        }


@dataclass
class PatentMetrics:
    """专利指标数据类"""
    total_patents: int
    technical_fields: int
    average_quality_score: float
    total_citations: int
    recent_patents_30d: int
    top_technical_fields: List[Tuple[str, int]]


class PatentDB:
    """专利数据库管理器 - 增强版本"""

    def __init__(self,
                 db_path: str = "patents.db",
                 enable_fts: bool = True,
                 enable_backup: bool = True,
                 backup_interval_hours: int = 24):
        """
        初始化专利数据库

        Args:
            db_path: 数据库文件路径
            enable_fts: 是否启用全文搜索
            enable_backup: 是否启用自动备份
            backup_interval_hours: 备份间隔小时数
        """
        self.db_path = Path(db_path)
        self.enable_fts = enable_fts
        self.enable_backup = enable_backup
        self.backup_interval = timedelta(hours=backup_interval_hours)

        # 线程安全
        self._lock = threading.RLock()

        # 日志记录器
        self.logger = logging.getLogger("AutoPatent.PatentDB")

        # 性能统计
        self.query_stats = {
            "total_queries": 0,
            "search_queries": 0,
            "insert_operations": 0,
            "update_operations": 0,
            "average_query_time": 0.0
        }

        # 缓存
        self._cache = {}
        self._cache_max_size = 1000
        self._cache_ttl = timedelta(hours=1)

        # 初始化数据库
        self._initialize_database()

        self.logger.info(f"PatentDB初始化完成: {self.db_path}")

    def _initialize_database(self):
        """初始化数据库schema和索引"""
        with self._get_connection() as conn:
            cursor = conn.cursor()

            # 创建主要专利表
            cursor.execute('''
                           CREATE TABLE IF NOT EXISTS patents
                           (
                               id
                               INTEGER
                               PRIMARY
                               KEY
                               AUTOINCREMENT,
                               patent_id
                               TEXT
                               UNIQUE
                               NOT
                               NULL,
                               title
                               TEXT
                               NOT
                               NULL,
                               abstract
                               TEXT,
                               claims
                               TEXT,
                               description
                               TEXT,
                               background
                               TEXT,
                               summary
                               TEXT,
                               detailed_description
                               TEXT,

                               -- 元数据
                               inventors
                               TEXT,
                               assignee
                               TEXT,
                               filing_date
                               TEXT,
                               publication_date
                               TEXT,
                               technical_field
                               TEXT,
                               ipc_classification
                               TEXT,

                               -- 生成相关
                               generation_status
                               TEXT
                               DEFAULT
                               'draft',
                               quality_score
                               REAL,
                               iteration_count
                               INTEGER
                               DEFAULT
                               0,
                               workflow_id
                               TEXT,

                               -- 内容统计
                               word_count
                               INTEGER
                               DEFAULT
                               0,
                               claims_count
                               INTEGER
                               DEFAULT
                               0,

                               -- 时间戳
                               created_at
                               TEXT
                               DEFAULT
                               CURRENT_TIMESTAMP,
                               updated_at
                               TEXT
                               DEFAULT
                               CURRENT_TIMESTAMP
                           )
                           ''')

            # 创建keywords表
            cursor.execute('''
                           CREATE TABLE IF NOT EXISTS keywords
                           (
                               id
                               INTEGER
                               PRIMARY
                               KEY
                               AUTOINCREMENT,
                               word
                               TEXT
                               UNIQUE
                               NOT
                               NULL,
                               frequency
                               INTEGER
                               DEFAULT
                               1,
                               technical_field
                               TEXT,
                               created_at
                               TEXT
                               DEFAULT
                               CURRENT_TIMESTAMP
                           )
                           ''')

            # 创建patent_keywords关联表
            cursor.execute('''
                           CREATE TABLE IF NOT EXISTS patent_keywords
                           (
                               patent_id
                               INTEGER,
                               keyword_id
                               INTEGER,
                               relevance_score
                               REAL
                               DEFAULT
                               1.0,
                               FOREIGN
                               KEY
                           (
                               patent_id
                           ) REFERENCES patents
                           (
                               id
                           ),
                               FOREIGN KEY
                           (
                               keyword_id
                           ) REFERENCES keywords
                           (
                               id
                           ),
                               PRIMARY KEY
                           (
                               patent_id,
                               keyword_id
                           )
                               )
                           ''')

            # 创建citations表
            cursor.execute('''
                           CREATE TABLE IF NOT EXISTS citations
                           (
                               id
                               INTEGER
                               PRIMARY
                               KEY
                               AUTOINCREMENT,
                               citing_patent_id
                               INTEGER,
                               cited_patent_id
                               INTEGER,
                               citation_type
                               TEXT
                               DEFAULT
                               'reference',
                               relevance_score
                               REAL
                               DEFAULT
                               1.0,
                               created_at
                               TEXT
                               DEFAULT
                               CURRENT_TIMESTAMP,
                               FOREIGN
                               KEY
                           (
                               citing_patent_id
                           ) REFERENCES patents
                           (
                               id
                           ),
                               FOREIGN KEY
                           (
                               cited_patent_id
                           ) REFERENCES patents
                           (
                               id
                           )
                               )
                           ''')

            # 创建search_history表
            cursor.execute('''
                           CREATE TABLE IF NOT EXISTS search_history
                           (
                               id
                               INTEGER
                               PRIMARY
                               KEY
                               AUTOINCREMENT,
                               query_text
                               TEXT
                               NOT
                               NULL,
                               query_type
                               TEXT,
                               results_count
                               INTEGER,
                               execution_time_ms
                               REAL,
                               user_id
                               TEXT,
                               created_at
                               TEXT
                               DEFAULT
                               CURRENT_TIMESTAMP
                           )
                           ''')

            # 创建backup_history表
            cursor.execute('''
                           CREATE TABLE IF NOT EXISTS backup_history
                           (
                               id
                               INTEGER
                               PRIMARY
                               KEY
                               AUTOINCREMENT,
                               backup_path
                               TEXT
                               NOT
                               NULL,
                               backup_size
                               INTEGER,
                               patents_count
                               INTEGER,
                               created_at
                               TEXT
                               DEFAULT
                               CURRENT_TIMESTAMP
                           )
                           ''')

            # 创建索引
            self._create_indexes(cursor)

            # 创建全文搜索索引（如果启用）
            if self.enable_fts:
                self._create_fts_indexes(cursor)

            conn.commit()
            self.logger.info("数据库schema初始化完成")

    def _create_indexes(self, cursor):
        """创建数据库索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_patents_patent_id ON patents(patent_id)",
            "CREATE INDEX IF NOT EXISTS idx_patents_technical_field ON patents(technical_field)",
            "CREATE INDEX IF NOT EXISTS idx_patents_filing_date ON patents(filing_date)",
            "CREATE INDEX IF NOT EXISTS idx_patents_quality_score ON patents(quality_score)",
            "CREATE INDEX IF NOT EXISTS idx_patents_status ON patents(generation_status)",
            "CREATE INDEX IF NOT EXISTS idx_patents_created_at ON patents(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_keywords_word ON keywords(word)",
            "CREATE INDEX IF NOT EXISTS idx_keywords_frequency ON keywords(frequency)",
            "CREATE INDEX IF NOT EXISTS idx_citations_citing ON citations(citing_patent_id)",
            "CREATE INDEX IF NOT EXISTS idx_citations_cited ON citations(cited_patent_id)",
        ]

        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except sqlite3.Error as e:
                self.logger.warning(f"创建索引失败: {e}")

    def _create_fts_indexes(self, cursor):
        """创建全文搜索索引"""
        try:
            # 创建FTS虚拟表
            cursor.execute('''
                CREATE VIRTUAL TABLE IF NOT EXISTS patents_fts USING fts5(
                    patent_id,
                    title,
                    abstract,
                    claims,
                    description,
                    technical_field,
                    content='patents',
                    content_rowid='id'
                )
            ''')

            # 创建触发器保持FTS表同步
            cursor.execute('''
                           CREATE TRIGGER IF NOT EXISTS patents_fts_insert AFTER INSERT ON patents
                           BEGIN
                    INSERT INTO patents_fts(rowid, patent_id, title, abstract, claims, description, technical_field)
                    VALUES (new.id, new.patent_id, new.title, new.abstract, new.claims, new.description, new.technical_field);
                           END
                           ''')

            cursor.execute('''
                           CREATE TRIGGER IF NOT EXISTS patents_fts_delete AFTER
                           DELETE
                           ON patents BEGIN
                    INSERT INTO patents_fts(patents_fts, rowid, patent_id, title, abstract, claims, description, technical_field)
                    VALUES('delete', old.id, old.patent_id, old.title, old.abstract, old.claims, old.description, old.technical_field);
                           END
                           ''')

            cursor.execute('''
                           CREATE TRIGGER IF NOT EXISTS patents_fts_update AFTER
                           UPDATE ON patents BEGIN
                           INSERT
                           INTO patents_fts(patents_fts, rowid, patent_id, title, abstract, claims, description,
                                            technical_field)
                           VALUES ('delete', old.id, old.patent_id, old.title, old.abstract, old.claims, old.description, old.technical_field);
                           INSERT INTO patents_fts(rowid, patent_id, title, abstract, claims, description,
                                                   technical_field)
                           VALUES (new.id, new.patent_id, new.title, new.abstract, new.claims, new.description,
                                   new.technical_field);
                           END
                           ''')

            self.logger.info("全文搜索索引创建完成")

        except sqlite3.Error as e:
            self.logger.error(f"创建全文搜索索引失败: {e}")
            self.enable_fts = False

    @contextmanager
    def _get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(str(self.db_path), timeout=30.0)
            conn.row_factory = sqlite3.Row  # 启用字典式访问
            # 启用外键约束
            conn.execute("PRAGMA foreign_keys = ON")
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def add_patent(self, patent_data: Dict[str, Any]) -> bool:
        """
        添加新专利到数据库

        Args:
            patent_data: 专利数据字典

        Returns:
            是否添加成功
        """
        start_time = datetime.now()

        with self._lock:
            try:
                # 验证必需字段
                required_fields = ['patent_id', 'title']
                for field in required_fields:
                    if field not in patent_data or not patent_data[field]:
                        raise ValueError(f"缺少必需字段: {field}")

                # 计算content统计
                word_count = self._calculate_word_count(patent_data)
                claims_count = self._calculate_claims_count(patent_data.get('claims', ''))

                # 准备插入数据
                insert_data = {
                    'patent_id': patent_data['patent_id'],
                    'title': patent_data['title'],
                    'abstract': patent_data.get('abstract', ''),
                    'claims': patent_data.get('claims', ''),
                    'description': patent_data.get('description', ''),
                    'background': patent_data.get('background', ''),
                    'summary': patent_data.get('summary', ''),
                    'detailed_description': patent_data.get('detailed_description', ''),
                    'inventors': patent_data.get('inventors', ''),
                    'assignee': patent_data.get('assignee', ''),
                    'filing_date': patent_data.get('filing_date', datetime.now().isoformat()),
                    'publication_date': patent_data.get('publication_date', ''),
                    'technical_field': patent_data.get('technical_field', ''),
                    'ipc_classification': patent_data.get('ipc_classification', ''),
                    'generation_status': patent_data.get('generation_status', 'draft'),
                    'quality_score': patent_data.get('quality_score', 0.0),
                    'iteration_count': patent_data.get('iteration_count', 0),
                    'workflow_id': patent_data.get('workflow_id', ''),
                    'word_count': word_count,
                    'claims_count': claims_count,
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                }

                with self._get_connection() as conn:
                    cursor = conn.cursor()

                    # 插入专利数据
                    placeholders = ', '.join(['?' for _ in insert_data])
                    columns = ', '.join(insert_data.keys())

                    cursor.execute(
                        f"INSERT INTO patents ({columns}) VALUES ({placeholders})",
                        list(insert_data.values())
                    )

                    patent_row_id = cursor.lastrowid

                    # 处理keywords
                    self._process_patent_keywords(cursor, patent_row_id, patent_data)

                    # 处理generation_metadata（如果存在）
                    if 'generation_metadata' in patent_data:
                        self._store_generation_metadata(patent_data['patent_id'], patent_data['generation_metadata'])

                    conn.commit()

                # 更新统计
                self.query_stats["insert_operations"] += 1
                execution_time = (datetime.now() - start_time).total_seconds()
                self._update_query_stats(execution_time)

                # 清除相关缓存
                self._invalidate_cache(['search', 'stats'])

                self.logger.info(f"专利添加成功: {patent_data['patent_id']}")
                return True

            except Exception as e:
                self.logger.error(f"添加专利失败: {e}")
                return False

    def search_patents(self,
                       query: str,
                       search_fields: List[str] = None,
                       technical_field: str = None,
                       limit: int = 10,
                       offset: int = 0,
                       min_quality_score: float = None,
                       date_range: Tuple[str, str] = None) -> List[SearchResult]:
        """
        搜索专利

        Args:
            query: 搜索查询文本
            search_fields: 搜索字段列表
            technical_field: 技术领域过滤
            limit: 结果数量限制
            offset: 结果偏移量
            min_quality_score: 最小质量评分
            date_range: 日期范围过滤 (start_date, end_date)

        Returns:
            搜索结果列表
        """
        start_time = datetime.now()

        # 检查缓存
        cache_key = self._generate_cache_key('search', query, search_fields, technical_field, limit, offset)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            return cached_result

        with self._lock:
            try:
                if self.enable_fts and not search_fields:
                    # 使用全文搜索
                    results = self._search_with_fts(query, technical_field, limit, offset, min_quality_score,
                                                    date_range)
                else:
                    # 使用常规搜索
                    results = self._search_with_like(query, search_fields, technical_field, limit, offset,
                                                     min_quality_score, date_range)

                # 计算relevance score
                for result in results:
                    result.relevance_score = self._calculate_relevance_score(query, result)

                # 按relevance score排序
                results.sort(key=lambda x: x.relevance_score, reverse=True)

                # 更新统计
                self.query_stats["search_queries"] += 1
                execution_time = (datetime.now() - start_time).total_seconds() * 1000  # 转换为毫秒
                self._update_query_stats(execution_time / 1000)

                # 记录搜索历史
                self._record_search_history(query, "general", len(results), execution_time)

                # 缓存结果
                self._cache_result(cache_key, results)

                self.logger.debug(f"搜索完成: {len(results)} 个结果，耗时 {execution_time:.2f}ms")
                return results

            except Exception as e:
                self.logger.error(f"搜索失败: {e}")
                return []

    def search_similar_patents(self,
                               concept: str,
                               limit: int = 5,
                               similarity_threshold: float = 0.3) -> List[SearchResult]:
        """
        搜索相似专利（用于RRAG）

        Args:
            concept: 专利概念或内容
            limit: 结果数量限制
            similarity_threshold: 相似度阈值

        Returns:
            相似专利列表
        """
        start_time = datetime.now()

        # 提取concept中的关键词
        keywords = self._extract_keywords(concept)

        if not keywords:
            self.logger.warning("无法从concept中提取关键词")
            return []

        with self._lock:
            try:
                # 构建相似性搜索查询
                search_query = ' '.join(keywords[:10])  # 限制关键词数量

                # 执行搜索
                results = self.search_patents(
                    query=search_query,
                    limit=limit * 2,  # 获取更多结果用于相似度计算
                    min_quality_score=5.0  # 只考虑较高质量的专利
                )

                # 计算详细相似度
                similar_results = []
                for result in results:
                    similarity_score = self._calculate_similarity_score(concept, result)

                    if similarity_score >= similarity_threshold:
                        result.similarity_score = similarity_score
                        result.matched_keywords = self._find_matched_keywords(keywords, result)
                        similar_results.append(result)

                # 按相似度排序并限制结果数量
                similar_results.sort(key=lambda x: x.similarity_score, reverse=True)
                similar_results = similar_results[:limit]

                # 记录搜索历史
                execution_time = (datetime.now() - start_time).total_seconds() * 1000
                self._record_search_history(concept[:100], "similarity", len(similar_results), execution_time)

                self.logger.info(f"相似专利搜索完成: {len(similar_results)} 个结果")
                return similar_results

            except Exception as e:
                self.logger.error(f"相似专利搜索失败: {e}")
                return []

    def _search_with_fts(self,
                         query: str,
                         technical_field: str = None,
                         limit: int = 10,
                         offset: int = 0,
                         min_quality_score: float = None,
                         date_range: Tuple[str, str] = None) -> List[SearchResult]:
        """使用全文搜索进行查询"""
        with self._get_connection() as conn:
            cursor = conn.cursor()

            # 构建FTS查询
            fts_query = self._build_fts_query(query)

            # 构建完整查询
            base_query = '''
                         SELECT p.patent_id, \
                                p.title, \
                                p.abstract, \
                                p.technical_field,
                                rank as relevance_score
                         FROM patents_fts fts
                                  JOIN patents p ON p.id = fts.rowid
                         WHERE patents_fts MATCH ? \
                         '''

            params = [fts_query]
            conditions = []

            # 添加过滤条件
            if technical_field:
                conditions.append("p.technical_field = ?")
                params.append(technical_field)

            if min_quality_score is not None:
                conditions.append("p.quality_score >= ?")
                params.append(min_quality_score)

            if date_range:
                conditions.append("p.filing_date BETWEEN ? AND ?")
                params.extend(date_range)

            if conditions:
                base_query += " AND " + " AND ".join(conditions)

            base_query += " ORDER BY rank LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            cursor.execute(base_query, params)
            rows = cursor.fetchall()

            return [SearchResult(
                patent_id=row['patent_id'],
                title=row['title'],
                abstract=row['abstract'] or '',
                technical_field=row['technical_field'] or '',
                relevance_score=row['relevance_score']
            ) for row in rows]

    def _search_with_like(self,
                          query: str,
                          search_fields: List[str] = None,
                          technical_field: str = None,
                          limit: int = 10,
                          offset: int = 0,
                          min_quality_score: float = None,
                          date_range: Tuple[str, str] = None) -> List[SearchResult]:
        """使用LIKE进行常规搜索"""
        if not search_fields:
            search_fields = ['title', 'abstract', 'claims', 'description']

        with self._get_connection() as conn:
            cursor = conn.cursor()

            # 构建搜索条件
            search_conditions = []
            params = []

            for field in search_fields:
                search_conditions.append(f"{field} LIKE ?")
                params.append(f"%{query}%")

            base_query = f'''
                SELECT patent_id, title, abstract, technical_field
                FROM patents
                WHERE ({' OR '.join(search_conditions)})
            '''

            # 添加过滤条件
            additional_conditions = []

            if technical_field:
                additional_conditions.append("technical_field = ?")
                params.append(technical_field)

            if min_quality_score is not None:
                additional_conditions.append("quality_score >= ?")
                params.append(min_quality_score)

            if date_range:
                additional_conditions.append("filing_date BETWEEN ? AND ?")
                params.extend(date_range)

            if additional_conditions:
                base_query += " AND " + " AND ".join(additional_conditions)

            base_query += " ORDER BY quality_score DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            cursor.execute(base_query, params)
            rows = cursor.fetchall()

            return [SearchResult(
                patent_id=row['patent_id'],
                title=row['title'],
                abstract=row['abstract'] or '',
                technical_field=row['technical_field'] or '',
                relevance_score=0.0  # 将在后续计算
            ) for row in rows]

    def get_patent_by_id(self, patent_id: str) -> Optional[Dict[str, Any]]:
        """
        根据patent_id获取完整专利信息

        Args:
            patent_id: 专利ID

        Returns:
            专利数据字典或None
        """
        # 检查缓存
        cache_key = f"patent_{patent_id}"
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            return cached_result

        with self._lock:
            try:
                with self._get_connection() as conn:
                    cursor = conn.cursor()

                    cursor.execute('''
                                   SELECT *
                                   FROM patents
                                   WHERE patent_id = ?
                                   ''', (patent_id,))

                    row = cursor.fetchone()

                    if row:
                        patent_data = dict(row)

                        # 获取关联的keywords
                        cursor.execute('''
                                       SELECT k.word, pk.relevance_score
                                       FROM keywords k
                                                JOIN patent_keywords pk ON k.id = pk.keyword_id
                                                JOIN patents p ON p.id = pk.patent_id
                                       WHERE p.patent_id = ?
                                       ORDER BY pk.relevance_score DESC
                                       ''', (patent_id,))

                        keywords = cursor.fetchall()
                        patent_data['keywords'] = [dict(kw) for kw in keywords]

                        # 缓存结果
                        self._cache_result(cache_key, patent_data)

                        return patent_data

                return None

            except Exception as e:
                self.logger.error(f"获取专利失败: {e}")
                return None

    def update_patent(self, patent_id: str, update_data: Dict[str, Any]) -> bool:
        """
        更新专利信息

        Args:
            patent_id: 专利ID
            update_data: 更新数据

        Returns:
            是否更新成功
        """
        with self._lock:
            try:
                # 添加更新时间戳
                update_data['updated_at'] = datetime.now().isoformat()

                # 重新计算统计数据
                if any(field in update_data for field in ['title', 'abstract', 'claims', 'description']):
                    update_data['word_count'] = self._calculate_word_count(update_data)

                if 'claims' in update_data:
                    update_data['claims_count'] = self._calculate_claims_count(update_data['claims'])

                with self._get_connection() as conn:
                    cursor = conn.cursor()

                    # 构建更新查询
                    set_clauses = [f"{key} = ?" for key in update_data.keys()]
                    values = list(update_data.values()) + [patent_id]

                    cursor.execute(f'''
                        UPDATE patents 
                        SET {', '.join(set_clauses)}
                        WHERE patent_id = ?
                    ''', values)

                    updated_rows = cursor.rowcount
                    conn.commit()

                if updated_rows > 0:
                    # 更新统计
                    self.query_stats["update_operations"] += 1

                    # 清除相关缓存
                    self._invalidate_cache([f'patent_{patent_id}', 'search', 'stats'])

                    self.logger.info(f"专利更新成功: {patent_id}")
                    return True
                else:
                    self.logger.warning(f"专利未找到: {patent_id}")
                    return False

            except Exception as e:
                self.logger.error(f"更新专利失败: {e}")
                return False

    def delete_patent(self, patent_id: str) -> bool:
        """
        删除专利

        Args:
            patent_id: 专利ID

        Returns:
            是否删除成功
        """
        with self._lock:
            try:
                with self._get_connection() as conn:
                    cursor = conn.cursor()

                    # 删除专利（级联删除会处理关联数据）
                    cursor.execute('DELETE FROM patents WHERE patent_id = ?', (patent_id,))

                    deleted_rows = cursor.rowcount
                    conn.commit()

                if deleted_rows > 0:
                    # 清除相关缓存
                    self._invalidate_cache([f'patent_{patent_id}', 'search', 'stats'])

                    self.logger.info(f"专利删除成功: {patent_id}")
                    return True
                else:
                    self.logger.warning(f"专利未找到: {patent_id}")
                    return False

            except Exception as e:
                self.logger.error(f"删除专利失败: {e}")
                return False

    def get_statistics(self) -> PatentMetrics:
        """
        获取数据库统计信息

        Returns:
            统计指标对象
        """
        # 检查缓存
        cache_key = "database_stats"
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            return cached_result

        with self._lock:
            try:
                with self._get_connection() as conn:
                    cursor = conn.cursor()

                    # 获取总专利数
                    cursor.execute('SELECT COUNT(*) FROM patents')
                    total_patents = cursor.fetchone()[0]

                    # 获取技术领域数
                    cursor.execute('SELECT COUNT(DISTINCT technical_field) FROM patents WHERE technical_field != ""')
                    technical_fields = cursor.fetchone()[0]

                    # 获取平均质量评分
                    cursor.execute('SELECT AVG(quality_score) FROM patents WHERE quality_score > 0')
                    avg_quality = cursor.fetchone()[0] or 0.0

                    # 获取总引用数
                    cursor.execute('SELECT COUNT(*) FROM citations')
                    total_citations = cursor.fetchone()[0]

                    # 获取最近30天的专利数
                    thirty_days_ago = (datetime.now() - timedelta(days=30)).isoformat()
                    cursor.execute('SELECT COUNT(*) FROM patents WHERE created_at >= ?', (thirty_days_ago,))
                    recent_patents = cursor.fetchone()[0]

                    # 获取top技术领域
                    cursor.execute('''
                                   SELECT technical_field, COUNT(*) as count
                                   FROM patents
                                   WHERE technical_field != ""
                                   GROUP BY technical_field
                                   ORDER BY count DESC
                                       LIMIT 10
                                   ''')
                    top_fields = cursor.fetchall()

                    stats = PatentMetrics(
                        total_patents=total_patents,
                        technical_fields=technical_fields,
                        average_quality_score=round(avg_quality, 2),
                        total_citations=total_citations,
                        recent_patents_30d=recent_patents,
                        top_technical_fields=[(row[0], row[1]) for row in top_fields]
                    )

                    # 缓存结果
                    self._cache_result(cache_key, stats)

                    return stats

            except Exception as e:
                self.logger.error(f"获取统计信息失败: {e}")
                return PatentMetrics(0, 0, 0.0, 0, 0, [])

    def add_citation(self,
                     citing_patent_id: str,
                     cited_patent_id: str,
                     citation_type: str = "reference",
                     relevance_score: float = 1.0) -> bool:
        """
        添加专利引用关系

        Args:
            citing_patent_id: 引用专利ID
            cited_patent_id: 被引用专利ID
            citation_type: 引用类型
            relevance_score: 相关性评分

        Returns:
            是否添加成功
        """
        with self._lock:
            try:
                with self._get_connection() as conn:
                    cursor = conn.cursor()

                    # 获取专利的内部ID
                    cursor.execute('SELECT id FROM patents WHERE patent_id = ?', (citing_patent_id,))
                    citing_row = cursor.fetchone()

                    cursor.execute('SELECT id FROM patents WHERE patent_id = ?', (cited_patent_id,))
                    cited_row = cursor.fetchone()

                    if not citing_row or not cited_row:
                        self.logger.error(f"专利不存在: {citing_patent_id} 或 {cited_patent_id}")
                        return False

                    # 插入引用关系
                    cursor.execute('''
                                   INSERT
                                   OR IGNORE INTO citations 
                        (citing_patent_id, cited_patent_id, citation_type, relevance_score)
                        VALUES (?, ?, ?, ?)
                                   ''', (citing_row[0], cited_row[0], citation_type, relevance_score))

                    conn.commit()

                    self.logger.debug(f"引用关系添加成功: {citing_patent_id} -> {cited_patent_id}")
                    return True

            except Exception as e:
                self.logger.error(f"添加引用关系失败: {e}")
                return False

    def backup_database(self, backup_path: str = None) -> bool:
        """
        备份数据库

        Args:
            backup_path: 备份文件路径

        Returns:
            是否备份成功
        """
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.db_path.stem}_backup_{timestamp}.db"

        try:
            with self._get_connection() as source_conn:
                backup_conn = sqlite3.connect(backup_path)
                source_conn.backup(backup_conn)
                backup_conn.close()

            # 记录备份历史
            backup_size = Path(backup_path).stat().st_size
            stats = self.get_statistics()

            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                               INSERT INTO backup_history (backup_path, backup_size, patents_count)
                               VALUES (?, ?, ?)
                               ''', (backup_path, backup_size, stats.total_patents))
                conn.commit()

            self.logger.info(f"数据库备份成功: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return False

    def _process_patent_keywords(self, cursor, patent_row_id: int, patent_data: Dict[str, Any]):
        """处理专利关键词"""
        # 从专利内容中提取关键词
        content_text = ' '.join([
            patent_data.get('title', ''),
            patent_data.get('abstract', ''),
            patent_data.get('claims', ''),
            patent_data.get('description', '')
        ])

        keywords = self._extract_keywords(content_text)

        for keyword in keywords[:20]:  # 限制关键词数量
            # 插入或更新keyword
            cursor.execute('''
                           INSERT
                           OR IGNORE INTO keywords (word, technical_field) 
                VALUES (?, ?)
                           ''', (keyword, patent_data.get('technical_field', '')))

            cursor.execute('UPDATE keywords SET frequency = frequency + 1 WHERE word = ?', (keyword,))

            # 获取keyword ID
            cursor.execute('SELECT id FROM keywords WHERE word = ?', (keyword,))
            keyword_id = cursor.fetchone()[0]

            # 计算relevance score
            relevance_score = self._calculate_keyword_relevance(keyword, content_text)

            # 插入patent_keywords关联
            cursor.execute('''
                           INSERT
                           OR IGNORE INTO patent_keywords (patent_id, keyword_id, relevance_score)
                VALUES (?, ?, ?)
                           ''', (patent_row_id, keyword_id, relevance_score))

    def _extract_keywords(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        if not text:
            return []

        # 简化的关键词提取（实际实现中可以使用更复杂的NLP方法）
        # 移除标点符号和特殊字符
        cleaned_text = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', text.lower())

        # 分词
        words = cleaned_text.split()

        # 过滤长度和常用词
        stop_words = {'的', '和', '是', '在', '一个', 'the', 'and', 'is', 'in', 'a', 'an', 'to', 'for', 'of', 'with'}
        keywords = [word for word in words
                    if len(word) > 2 and word not in stop_words]

        # 去重并保持顺序
        seen = set()
        unique_keywords = []
        for keyword in keywords:
            if keyword not in seen:
                seen.add(keyword)
                unique_keywords.append(keyword)

        return unique_keywords[:50]  # 限制数量

    def _calculate_word_count(self, patent_data: Dict[str, Any]) -> int:
        """计算专利总词数"""
        content_fields = ['title', 'abstract', 'claims', 'description', 'background', 'summary', 'detailed_description']
        total_words = 0

        for field in content_fields:
            content = patent_data.get(field, '')
            if content:
                total_words += len(content.split())

        return total_words

    def _calculate_claims_count(self, claims_text: str) -> int:
        """计算权利要求数量"""
        if not claims_text:
            return 0

        # 简单计算：基于数字开头的行数
        lines = claims_text.split('\n')
        claim_count = 0

        for line in lines:
            line = line.strip()
            if line and (line[0].isdigit() or line.startswith('权利要求')):
                claim_count += 1

        return max(claim_count, 1)  # 至少1个

    def _calculate_relevance_score(self, query: str, result: SearchResult) -> float:
        """计算搜索结果的相关性评分"""
        score = 0.0
        query_words = set(query.lower().split())

        # 标题匹配权重更高
        title_words = set(result.title.lower().split())
        title_matches = len(query_words.intersection(title_words))
        score += title_matches * 3.0

        # 摘要匹配
        abstract_words = set(result.abstract.lower().split())
        abstract_matches = len(query_words.intersection(abstract_words))
        score += abstract_matches * 1.5

        # 技术领域匹配
        if query.lower() in result.technical_field.lower():
            score += 2.0

        # 标准化评分
        max_possible_score = len(query_words) * 3.0 + 2.0
        return min(score / max_possible_score, 1.0) if max_possible_score > 0 else 0.0

    def _calculate_similarity_score(self, concept: str, result: SearchResult) -> float:
        """计算专利相似度评分"""
        concept_words = set(self._extract_keywords(concept))

        # 从result中提取words
        result_text = f"{result.title} {result.abstract}"
        result_words = set(self._extract_keywords(result_text))

        if not concept_words or not result_words:
            return 0.0

        # 计算Jaccard相似度
        intersection = concept_words.intersection(result_words)
        union = concept_words.union(result_words)

        return len(intersection) / len(union) if union else 0.0

    def _find_matched_keywords(self, concept_keywords: List[str], result: SearchResult) -> List[str]:
        """找到匹配的关键词"""
        result_text = f"{result.title} {result.abstract}".lower()
        matched = []

        for keyword in concept_keywords:
            if keyword.lower() in result_text:
                matched.append(keyword)

        return matched

    def _calculate_keyword_relevance(self, keyword: str, text: str) -> float:
        """计算关键词在文本中的相关性"""
        if not keyword or not text:
            return 0.0

        text_lower = text.lower()
        keyword_lower = keyword.lower()

        # 计算词频
        count = text_lower.count(keyword_lower)
        total_words = len(text.split())

        if total_words == 0:
            return 0.0

        # TF计算
        tf = count / total_words

        # 简化的相关性评分
        return min(tf * 10, 1.0)

    def _build_fts_query(self, query: str) -> str:
        """构建FTS查询字符串"""
        # 简单的查询预处理
        words = query.split()

        # 对每个词添加前缀匹配
        fts_terms = []
        for word in words:
            if len(word) > 2:
                fts_terms.append(f'"{word}"*')

        return ' OR '.join(fts_terms) if fts_terms else query

    def _store_generation_metadata(self, patent_id: str, metadata: Dict[str, Any]):
        """存储generation metadata（作为JSON）"""
        # 可以存储在单独的metadata表中，这里简化处理
        metadata_json = json.dumps(metadata, ensure_ascii=False)

        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                           UPDATE patents
                           SET generation_metadata = ?
                           WHERE patent_id = ?
                           ''', (metadata_json, patent_id))
            conn.commit()

    def _record_search_history(self, query: str, query_type: str, results_count: int, execution_time_ms: float):
        """记录搜索历史"""
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                               INSERT INTO search_history
                                   (query_text, query_type, results_count, execution_time_ms, user_id)
                               VALUES (?, ?, ?, ?, ?)
                               ''', (query, query_type, results_count, execution_time_ms, "longwarriors"))
                conn.commit()
        except Exception as e:
            self.logger.error(f"记录搜索历史失败: {e}")

    def _update_query_stats(self, execution_time: float):
        """更新查询统计"""
        self.query_stats["total_queries"] += 1

        # 更新平均查询时间
        total_time = self.query_stats["average_query_time"] * (self.query_stats["total_queries"] - 1)
        self.query_stats["average_query_time"] = (total_time + execution_time) / self.query_stats["total_queries"]

    def _generate_cache_key(self, *args) -> str:
        """生成缓存key"""
        key_string = '_'.join(str(arg) for arg in args)
        return hashlib.md5(key_string.encode()).hexdigest()

    def _get_cached_result(self, cache_key: str):
        """获取缓存结果"""
        if cache_key in self._cache:
            cached_item = self._cache[cache_key]
            if datetime.now() - cached_item['timestamp'] < self._cache_ttl:
                return cached_item['data']
            else:
                # 过期，删除缓存
                del self._cache[cache_key]
        return None

    def _cache_result(self, cache_key: str, data):
        """缓存结果"""
        # 如果缓存已满，删除最旧的条目
        if len(self._cache) >= self._cache_max_size:
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k]['timestamp'])
            del self._cache[oldest_key]

        self._cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now()
        }

    def _invalidate_cache(self, prefixes: List[str]):
        """使缓存失效"""
        keys_to_remove = []
        for key in self._cache:
            for prefix in prefixes:
                if prefix in key:
                    keys_to_remove.append(key)
                    break

        for key in keys_to_remove:
            del self._cache[key]

    def get_query_performance(self) -> Dict[str, Any]:
        """获取查询性能统计"""
        return {
            "query_stats": self.query_stats.copy(),
            "cache_stats": {
                "cache_size": len(self._cache),
                "cache_max_size": self._cache_max_size,
                "cache_hit_ratio": self._calculate_cache_hit_ratio()
            },
            "database_info": {
                "db_path": str(self.db_path),
                "db_size_mb": self.db_path.stat().st_size / (1024 * 1024) if self.db_path.exists() else 0,
                "fts_enabled": self.enable_fts
            }
        }

    def _calculate_cache_hit_ratio(self) -> float:
        """计算缓存命中率"""
        # 简化实现，实际中需要更详细的统计
        return 0.0  # 占位符

    def close(self):
        """关闭数据库连接"""
        # 清理缓存
        self._cache.clear()

        # 如果有持久连接，在这里关闭
        self.logger.info("PatentDB已关闭")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


if __name__ == '__main__':
    # 基本使用
    with PatentDB("patents.db") as db:
        # 添加专利
        patent_data = {
            'patent_id': 'CN123456789A',
            'title': '一种智能家居能耗管理系统',
            'abstract': '本发明提供了一种基于机器学习的...',
            'technical_field': '智能家居技术'
        }
        db.add_patent(patent_data)

        # 搜索专利
        results = db.search_patents("机器学习", limit=10)

        # 相似专利搜索（用于RRAG）
        similar = db.search_similar_patents("智能能耗管理", limit=5)

        # 获取统计信息
        stats = db.get_statistics()
        print(f"总专利数: {stats.total_patents}")
