"""
Token使用可视化工具 - 生成图表和报告
"""

from typing import Dict, Any, List, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import pandas as pd
from pathlib import Path
import os
import platform
import json
import glob

from utils import TokenTracker, OperationType


class TokenUsageVisualizer:
    """Token使用可视化器"""

    def __init__(self, token_tracker: Optional[TokenTracker] = None):
        """
        初始化可视化器

        Args:
            token_tracker: TokenTracker实例，如果不提供，将在使用导出文件时临时创建
        """
        self.token_tracker = token_tracker
        self._session_data = None  # 用于存储从导出文件中加载的数据

        # 设置适合中文显示的字体
        self._setup_chinese_font()

        # 设置样式
        sns.set_style("whitegrid")
        sns.set_palette("husl")

    def _setup_chinese_font(self):
        """设置中文字体"""
        system = platform.system()

        # 默认字体列表，根据操作系统配置不同选项
        if system == 'Windows':
            # Windows上常见的中文字体
            font_list = ['Microsoft YaHei', 'SimHei', 'SimSun', 'NSimSun', 'FangSong', 'KaiTi']
        elif system == 'Darwin':  # macOS
            font_list = ['Arial Unicode MS', 'Heiti TC', 'STHeiti', 'Songti SC']
        else:  # Linux和其他系统
            font_list = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Droid Sans Fallback']

        # 添加通用字体
        font_list.extend(['DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif'])

        # 设置字体
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = font_list
        plt.rcParams['axes.unicode_minus'] = False

        # 检测并使用第一个可用的中文字体
        self._use_first_available_font(font_list)

    def _use_first_available_font(self, font_list):
        """检测并使用第一个可用的字体"""
        # 通过创建临时图形来检测字体是否可用
        available_fonts = []
        for font in font_list:
            try:
                fig = plt.figure(figsize=(1, 1))
                ax = fig.add_subplot(111)
                ax.text(0.5, 0.5, '中文测试', fontdict={'family': font})
                plt.close(fig)
                available_fonts.append(font)
            except:
                continue

        if available_fonts:
            # 使用第一个可用的字体
            plt.rcParams['font.sans-serif'] = available_fonts + ['sans-serif']
            print(f"使用字体: {available_fonts[0]}")
        else:
            print("警告: 未找到支持中文的字体，图表中的中文可能显示异常")
            # 尝试设置英文图表标题
            self._use_english_labels = True

    @classmethod
    def from_export_file(cls, export_file_path: str) -> 'TokenUsageVisualizer':
        """
        从导出文件创建可视化器实例

        Args:
            export_file_path: 导出文件的路径 (JSON、CSV、XLSX)

        Returns:
            TokenUsageVisualizer实例
        """
        instance = cls()
        instance.load_from_export(export_file_path)
        return instance

    def load_from_export(self, export_file_path: str) -> None:
        """
        从导出的文件加载数据

        Args:
            export_file_path: 导出文件的路径 (JSON、CSV、XLSX)
        """
        # 确认文件存在
        if not os.path.exists(export_file_path):
            raise FileNotFoundError(f"文件不存在: {export_file_path}")

        # 根据文件类型加载数据
        file_extension = os.path.splitext(export_file_path)[1].lower()

        if file_extension == '.json':
            with open(export_file_path, 'r', encoding='utf-8') as f:
                self._session_data = json.load(f)

        elif file_extension in ['.xlsx', '.xls']:
            # 读取Excel文件的多个表格
            self._session_data = {}
            excel_data = pd.ExcelFile(export_file_path)

            # 读取Session统计表
            if 'Session统计' in excel_data.sheet_names:
                session_df = pd.read_excel(excel_data, 'Session统计')
                self._session_data["session统计"] = session_df.iloc[0].to_dict() if not session_df.empty else {}

            # 读取使用记录表
            if '使用记录' in excel_data.sheet_names:
                records_df = pd.read_excel(excel_data, '使用记录')
                self._session_data["原始记录"] = records_df.to_dict('records') if not records_df.empty else []

            # 读取成本分析表
            if '成本分析' in excel_data.sheet_names:
                cost_df = pd.read_excel(excel_data, '成本分析')
                self._session_data["成本估算"] = cost_df.iloc[0].to_dict() if not cost_df.empty else {}

        elif file_extension == '.csv':
            # CSV通常只包含使用记录
            records_df = pd.read_csv(export_file_path)
            self._session_data = {
                "原始记录": records_df.to_dict('records')
            }

            # 从记录中提取session_id
            session_ids = records_df['session_id'].unique() if 'session_id' in records_df.columns else []
            self._session_data["session统计"] = {"session_id": session_ids[0]} if session_ids else {}

        else:
            raise ValueError(f"不支持的文件格式: {file_extension}")

        print(f"已从文件 {export_file_path} 加载数据")

    def merge_export_files(self, file_paths: List[str]) -> None:
        """
        合并多个导出文件的数据

        Args:
            file_paths: 导出文件路径列表
        """
        all_records = []
        all_sessions = {}

        for file_path in file_paths:
            # 临时加载数据
            temp_visualizer = TokenUsageVisualizer()
            temp_visualizer.load_from_export(file_path)

            # 提取原始记录
            if temp_visualizer._session_data and "原始记录" in temp_visualizer._session_data:
                all_records.extend(temp_visualizer._session_data["原始记录"])

            # 提取session统计
            if temp_visualizer._session_data and "session统计" in temp_visualizer._session_data:
                session_id = temp_visualizer._session_data["session统计"].get("session_id")
                if session_id:
                    all_sessions[session_id] = temp_visualizer._session_data["session统计"]

        # 创建合并数据
        self._session_data = {
            "原始记录": all_records,
            "session统计": {
                "session_id": "merged_sessions",
                "merged_sessions": list(all_sessions.keys())
            }
        }

        print(f"已合并 {len(file_paths)} 个文件的数据，共 {len(all_records)} 条记录")

    def load_all_exports_in_directory(self, directory: str, pattern: str = "*token_usage*.json") -> None:
        """
        加载目录中所有符合模式的导出文件

        Args:
            directory: 目录路径
            pattern: 文件名匹配模式
        """
        # 获取所有匹配的文件
        file_path_pattern = os.path.join(directory, pattern)
        file_paths = glob.glob(file_path_pattern)

        if not file_paths:
            raise FileNotFoundError(f"目录 {directory} 中没有找到匹配 {pattern} 的文件")

        # 合并所有文件
        self.merge_export_files(file_paths)

    def get_usage_data(self) -> Dict[str, Any]:
        """
        获取当前使用的数据，优先使用TokenTracker，其次使用加载的导出文件数据

        Returns:
            使用统计数据
        """
        if self.token_tracker:
            return self.token_tracker.get_usage()
        elif self._session_data and "session统计" in self._session_data:
            return self._session_data["session统计"]
        else:
            raise ValueError("没有可用的使用数据，请先加载数据或提供TokenTracker实例")

    def get_detailed_data(self) -> Dict[str, Any]:
        """
        获取当前的详细使用数据

        Returns:
            详细使用数据
        """
        if self.token_tracker:
            return self.token_tracker.get_detailed_usage()
        elif self._session_data and "原始记录" in self._session_data:
            # 从原始记录重建详细分析
            # 这里需要实现类似TokenTracker.get_detailed_usage的逻辑
            # 简化实现：按时间和操作类型分组统计
            records = self._session_data["原始记录"]

            # 时间趋势分析
            time_trends = {}
            operation_stats = {}
            agent_stats = {}
            model_stats = {}

            for record in records:
                # 处理时间戳
                if 'timestamp' in record:
                    try:
                        timestamp = datetime.fromisoformat(record['timestamp'])
                        hour_key = timestamp.strftime("%Y-%m-%d %H:00")

                        if hour_key not in time_trends:
                            time_trends[hour_key] = {"tokens": 0, "成本": 0.0, "调用次数": 0}

                        time_trends[hour_key]["tokens"] += record.get('count', 0)
                        time_trends[hour_key]["成本"] += record.get('cost_estimate', 0.0)
                        time_trends[hour_key]["调用次数"] += 1
                    except:
                        pass

                # 操作类型统计
                operation = record.get('operation', 'unknown')
                if operation not in operation_stats:
                    operation_stats[operation] = {"次数": 0, "tokens": 0, "成本": 0.0}

                operation_stats[operation]["次数"] += 1
                operation_stats[operation]["tokens"] += record.get('count', 0)
                operation_stats[operation]["成本"] += record.get('cost_estimate', 0.0)

                # 智能体统计
                agent = record.get('agent_name', 'unknown')
                if agent not in agent_stats:
                    agent_stats[agent] = {"次数": 0, "tokens": 0, "成本": 0.0}

                agent_stats[agent]["次数"] += 1
                agent_stats[agent]["tokens"] += record.get('count', 0)
                agent_stats[agent]["成本"] += record.get('cost_estimate', 0.0)

                # 模型统计
                model = record.get('model_name', 'unknown')
                if model not in model_stats:
                    model_stats[model] = {"次数": 0, "tokens": 0, "成本": 0.0}

                model_stats[model]["次数"] += 1
                model_stats[model]["tokens"] += record.get('count', 0)
                model_stats[model]["成本"] += record.get('cost_estimate', 0.0)

            return {
                "时间趋势": time_trends,
                "操作统计": operation_stats,
                "智能体统计": agent_stats,
                "模型统计": model_stats,
                "总记录数": len(records)
            }
        else:
            raise ValueError("没有可用的详细使用数据")

    def get_efficiency_data(self) -> Dict[str, Any]:
        """
        获取效率指标数据

        Returns:
            效率指标数据
        """
        if self.token_tracker:
            return self.token_tracker.get_efficiency_metrics()
        elif self._session_data and "原始记录" in self._session_data:
            # 从原始记录重建效率指标
            records = self._session_data["原始记录"]

            # 按操作分组计算效率指标
            operation_metrics = {}

            for record in records:
                operation = record.get('operation', 'unknown')
                if operation not in operation_metrics:
                    operation_metrics[operation] = {"总tokens": 0, "总时间": 0.0, "总成本": 0.0, "调用次数": 0}

                operation_metrics[operation]["总tokens"] += record.get('count', 0)
                operation_metrics[operation]["总时间"] += record.get('response_time', 0.0)
                operation_metrics[operation]["总成本"] += record.get('cost_estimate', 0.0)
                operation_metrics[operation]["调用次数"] += 1

            # 计算操作效率排名
            efficiency_list = []
            for operation, metrics in operation_metrics.items():
                if metrics["调用次数"] > 0:
                    tokens_per_second = (metrics["总tokens"] / metrics["总时间"]) if metrics["总时间"] > 0 else 0
                    cost_per_token = metrics["总成本"] / metrics["总tokens"] if metrics["总tokens"] > 0 else 0

                    efficiency_list.append({
                        "操作": operation,
                        "调用次数": metrics["调用次数"],
                        "token处理速度": round(tokens_per_second, 2),
                        "单token成本": round(cost_per_token, 8),
                        "平均响应时间": round(metrics["总时间"] / metrics["调用次数"], 2)
                    })

            # 按token处理速度排序
            efficiency_list.sort(key=lambda x: x["token处理速度"], reverse=True)

            return {"操作效率排名": efficiency_list}
        else:
            raise ValueError("没有可用的效率数据")

    def create_usage_dashboard(self, output_dir: str = "token_reports") -> str:
        """
        创建使用情况dashboard

        Args:
            output_dir: 输出目录

        Returns:
            生成的HTML报告路径
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 生成各种图表
        self._create_overview_charts(output_path)
        self._create_trend_charts(output_path)
        self._create_efficiency_charts(output_path)
        self._create_cost_charts(output_path)

        # 如果有多个session，生成对比图表
        try:
            if (self._session_data and "session统计" in self._session_data and
                "merged_sessions" in self._session_data["session统计"]):
                self._create_session_comparison_charts(output_path)
        except Exception as e:
            print(f"生成会话对比图表时出错: {e}")

        # 生成HTML报告
        html_path = output_path / "token_usage_dashboard.html"
        self._generate_html_report(html_path, output_path)

        return str(html_path)

    def _create_overview_charts(self, output_path: Path):
        """创建概览图表"""
        try:
            session_stats = self.get_usage_data()
        except ValueError as e:
            print(f"获取使用数据失败: {e}")
            return

        # Token使用分布饼图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Token Usage Overview', fontsize=16, fontweight='bold')

        # 1. Token类型分布
        token_data = {
            'Input Tokens': session_stats.get('total_input_tokens', 0),
            'Output Tokens': session_stats.get('total_output_tokens', 0),
            'Reasoning Tokens': session_stats.get('total_reasoning_tokens', 0)
        }

        if sum(token_data.values()) > 0:
            ax1.pie(token_data.values(), labels=token_data.keys(), autopct='%1.1f%%')
            ax1.set_title('Token Type Distribution')

        # 2. 操作类型分布
        operations = session_stats.get('operations', {})
        if operations:
            ax2.pie(operations.values(), labels=operations.keys(), autopct='%1.1f%%')
            ax2.set_title('Operation Type Distribution')

        # 3. 智能体使用分布
        agents = session_stats.get('agents_used', {})
        if agents:
            ax3.bar(agents.keys(), agents.values())
            ax3.set_title('Agent Call Count')
            ax3.tick_params(axis='x', rotation=45)

        # 4. 模型使用分布
        models = session_stats.get('models_used', {})
        if models:
            ax4.bar(models.keys(), models.values())
            ax4.set_title('Model Usage Count')
            ax4.tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(output_path / 'overview.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _create_trend_charts(self, output_path: Path):
        """创建趋势图表"""
        try:
            analysis = self.get_detailed_data()
        except ValueError as e:
            print(f"获取详细使用数据失败: {e}")
            return

        if '时间趋势' not in analysis:
            return

        trend_data = analysis['时间趋势']

        # 转换为DataFrame
        df_data = []
        for time_key, stats in trend_data.items():
            try:
                df_data.append({
                    '时间': datetime.strptime(time_key, "%Y-%m-%d %H:%M"),
                    'tokens': stats['tokens'],
                    '成本': stats['成本'],
                    '调用次数': stats['调用次数']
                })
            except ValueError:
                # 如果时间格式不匹配，尝试其他格式
                try:
                    df_data.append({
                        '时间': datetime.strptime(time_key, "%Y-%m-%d %H:00"),
                        'tokens': stats['tokens'],
                        '成本': stats['成本'],
                        '调用次数': stats['调用次数']
                    })
                except Exception:
                    continue

        if not df_data:
            return

        df = pd.DataFrame(df_data)
        df = df.sort_values('时间')

        # 创建趋势图
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
        fig.suptitle('Token Usage Trend Analysis', fontsize=16, fontweight='bold')

        # Token使用趋势
        ax1.plot(df['时间'], df['tokens'], marker='o', linewidth=2)
        ax1.set_title('Token Usage Trend')
        ax1.set_ylabel('Token Count')
        ax1.grid(True, alpha=0.3)

        # 成本趋势
        ax2.plot(df['时间'], df['成本'], marker='s', color='red', linewidth=2)
        ax2.set_title('Cost Trend')
        ax2.set_ylabel('Cost (USD)')
        ax2.grid(True, alpha=0.3)

        # API调用趋势
        ax3.bar(df['时间'], df['调用次数'], alpha=0.7, color='green')
        ax3.set_title('API Call Trend')
        ax3.set_ylabel('Call Count')
        ax3.set_xlabel('Time')

        plt.tight_layout()
        plt.savefig(output_path / 'trends.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _create_efficiency_charts(self, output_path: Path):
        """创建效率图表"""
        try:
            efficiency = self.get_efficiency_data()
        except ValueError as e:
            print(f"获取效率数据失败: {e}")
            return

        if '操作效率排名' not in efficiency:
            return

        rankings = efficiency['操作效率排名']

        if not rankings:
            return

        df = pd.DataFrame(rankings)

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Efficiency Analysis', fontsize=16, fontweight='bold')

        # Token处理速度
        ax1.barh(df['操作'], df['token处理速度'])
        ax1.set_title('Token Processing Speed (tokens/sec)')
        ax1.set_xlabel('Tokens/sec')

        # 单token成本
        ax2.barh(df['操作'], df['单token成本'])
        ax2.set_title('Single Token Cost (USD)')
        ax2.set_xlabel('Cost')

        # 平均响应时间
        ax3.barh(df['操作'], df['平均响应时间'])
        ax3.set_title('Average Response Time (sec)')
        ax3.set_xlabel('Time (sec)')

        # 调用次数
        ax4.barh(df['操作'], df['调用次数'])
        ax4.set_title('Operation Call Count')
        ax4.set_xlabel('Count')

        plt.tight_layout()
        plt.savefig(output_path / 'efficiency.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _create_cost_charts(self, output_path: Path):
        """创建成本图表"""
        try:
            detailed_usage = self.get_detailed_data()
        except ValueError as e:
            print(f"获取详细使用数据失败: {e}")
            return

        if '操作统计' not in detailed_usage:
            return

        operation_stats = detailed_usage['操作统计']

        # 成本分布
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('Cost Analysis', fontsize=16, fontweight='bold')

        # 成本分布饼图
        costs = [data['成本'] for data in operation_stats.values()]
        labels = list(operation_stats.keys())

        if sum(costs) > 0:
            ax1.pie(costs, labels=labels, autopct='%1.1f%%')
            ax1.set_title('Cost Distribution')

            # 成本条形图
            ax2.bar(labels, costs)
            ax2.set_title('Cost Details')
            ax2.set_ylabel('Cost (USD)')
            ax2.tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(output_path / 'costs.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _create_session_comparison_charts(self, output_path: Path):
        """创建会话对比图表"""
        # 只有在合并了多个会话时才会生成此图表
        if not (self._session_data and "原始记录" in self._session_data):
            return

        records = self._session_data["原始记录"]

        # 按会话ID分组
        session_groups = {}
        for record in records:
            session_id = record.get('session_id', 'unknown')
            if session_id not in session_groups:
                session_groups[session_id] = []
            session_groups[session_id].append(record)

        if len(session_groups) <= 1:
            return

        # 计算每个会话的统计信息
        session_stats = {}
        for session_id, session_records in session_groups.items():
            total_tokens = sum(record.get('count', 0) for record in session_records)
            total_cost = sum(record.get('cost_estimate', 0) for record in session_records)
            api_calls = len(session_records)

            # 模型使用统计
            models = {}
            for record in session_records:
                model = record.get('model_name', 'unknown')
                models[model] = models.get(model, 0) + 1

            # 操作类型统计
            operations = {}
            for record in session_records:
                operation = record.get('operation', 'unknown')
                operations[operation] = operations.get(operation, 0) + 1

            session_stats[session_id] = {
                'total_tokens': total_tokens,
                'total_cost': total_cost,
                'api_calls': api_calls,
                'models': models,
                'operations': operations
            }

        # 创建会话对比图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Session Comparison', fontsize=16, fontweight='bold')

        # 1. 总token数对比
        session_ids = list(session_stats.keys())
        total_tokens = [stats['total_tokens'] for stats in session_stats.values()]
        ax1.bar(session_ids, total_tokens)
        ax1.set_title('Total Tokens by Session')
        ax1.set_ylabel('Token Count')
        ax1.tick_params(axis='x', rotation=45)

        # 2. 总成本对比
        total_costs = [stats['total_cost'] for stats in session_stats.values()]
        ax2.bar(session_ids, total_costs)
        ax2.set_title('Total Cost by Session')
        ax2.set_ylabel('Cost (USD)')
        ax2.tick_params(axis='x', rotation=45)

        # 3. API调用次数对比
        api_calls = [stats['api_calls'] for stats in session_stats.values()]
        ax3.bar(session_ids, api_calls)
        ax3.set_title('API Calls by Session')
        ax3.set_ylabel('Call Count')
        ax3.tick_params(axis='x', rotation=45)

        # 4. 操作类型分布对比 (选择一个关键操作)
        all_operations = set()
        for stats in session_stats.values():
            all_operations.update(stats['operations'].keys())

        if all_operations:
            # 选择第一个操作类型作为示例
            operation_key = list(all_operations)[0]
            operation_counts = [stats['operations'].get(operation_key, 0) for stats in session_stats.values()]
            ax4.bar(session_ids, operation_counts)
            ax4.set_title(f'"{operation_key}" Operation Count by Session')
            ax4.set_ylabel('Count')
            ax4.tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(output_path / 'session_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 创建模型使用对比图
        self._create_model_comparison_chart(output_path, session_stats)

    def _create_model_comparison_chart(self, output_path: Path, session_stats: Dict[str, Dict[str, Any]]):
        """创建模型使用对比图表"""
        # 获取所有唯一的模型名称
        all_models = set()
        for stats in session_stats.values():
            all_models.update(stats.get('models', {}).keys())

        if not all_models:
            return

        # 准备数据
        model_data = []
        for session_id, stats in session_stats.items():
            for model in all_models:
                model_data.append({
                    'session_id': session_id,
                    'model': model,
                    'count': stats.get('models', {}).get(model, 0)
                })

        df = pd.DataFrame(model_data)

        # 创建模型使用对比图
        plt.figure(figsize=(12, 8))
        sns.barplot(x='model', y='count', hue='session_id', data=df)
        plt.title('Model Usage Comparison Across Sessions', fontsize=16)
        plt.xlabel('Model')
        plt.ylabel('Usage Count')
        plt.xticks(rotation=45)
        plt.legend(title='Session ID')
        plt.tight_layout()
        plt.savefig(output_path / 'model_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _generate_html_report(self, html_path: Path, charts_path: Path):
        """生成HTML报告"""
        try:
            session_stats = self.get_usage_data()
        except ValueError as e:
            print(f"获取使用数据失败，使用默认值: {e}")
            session_stats = {
                'session_id': 'unknown',
                'total_tokens': 0,
                'total_cost': 0,
                'api_calls': 0,
                'success_rate': 0
            }

        # 检测是否有会话比较图表
        has_comparison = (self._session_data and "session统计" in self._session_data and
                         "merged_sessions" in self._session_data["session统计"])

        # 为多会话情况添加会话列表
        sessions_list = ""
        if has_comparison:
            merged_sessions = self._session_data["session统计"].get("merged_sessions", [])
            sessions_list = """
            <div class="section">
                <h2 class="section-title">Merged Sessions</h2>
                <ul class="sessions-list">
            """
            for session in merged_sessions:
                sessions_list += f"<li>{session}</li>"

            sessions_list += """
                </ul>
            </div>
            """

        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AutoPatent Token Usage Report</title>
            <style>
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
                .header {{ text-align: center; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }}
                .title {{ color: #2c3e50; font-size: 2.5em; margin-bottom: 10px; }}
                .subtitle {{ color: #7f8c8d; font-size: 1.2em; }}
                .section {{ margin: 30px 0; }}
                .section-title {{ color: #34495e; font-size: 1.5em; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #ecf0f1; }}
                .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }}
                .stat-card {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }}
                .stat-value {{ font-size: 2em; font-weight: bold; margin-bottom: 5px; }}
                .stat-label {{ font-size: 0.9em; opacity: 0.9; }}
                .chart {{ text-align: center; margin: 20px 0; }}
                .chart img {{ max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .footer {{ text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #7f8c8d; }}
                .sessions-list {{ columns: 3; column-gap: 20px; list-style-type: none; padding: 0; }}
                .sessions-list li {{ margin-bottom: 5px; break-inside: avoid-column; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 class="title">AutoPatent Token Usage Report</h1>
                    <p class="subtitle">Session ID: {session_stats.get('session_id', 'unknown')}</p>
                    <p class="subtitle">Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>

                {sessions_list}

                <div class="section">
                    <h2 class="section-title">Session Overview</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">{session_stats.get('total_tokens', 0):,}</div>
                            <div class="stat-label">Total Tokens</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${session_stats.get('total_cost', 0):.4f}</div>
                            <div class="stat-label">Total Cost</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{session_stats.get('api_calls', 0)}</div>
                            <div class="stat-label">API Calls</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{session_stats.get('success_rate', 0):.1f}%</div>
                            <div class="stat-label">Success Rate</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">Usage Overview</h2>
                    <div class="chart">
                        <img src="overview.png" alt="Usage Overview Chart">
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">Usage Trends</h2>
                    <div class="chart">
                        <img src="trends.png" alt="Usage Trends Chart">
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">Efficiency Analysis</h2>
                    <div class="chart">
                        <img src="efficiency.png" alt="Efficiency Analysis Chart">
                    </div>
                </div>

                <div class="section">
                    <h2 class="section-title">Cost Analysis</h2>
                    <div class="chart">
                        <img src="costs.png" alt="Cost Analysis Chart">
                    </div>
                </div>
                """

        # 如果有会话比较图表，添加到报告中
        if has_comparison:
            html_content += """
                <div class="section">
                    <h2 class="section-title">Session Comparison</h2>
                    <div class="chart">
                        <img src="session_comparison.png" alt="Session Comparison Chart">
                    </div>
                </div>
                
                <div class="section">
                    <h2 class="section-title">Model Usage Comparison</h2>
                    <div class="chart">
                        <img src="model_comparison.png" alt="Model Comparison Chart">
                    </div>
                </div>
            """

        html_content += """
                <div class="footer">
                    <p>Report generated by AutoPatent TokenTracker</p>
                </div>
            </div>
        </body>
        </html>
        """

        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)


if __name__ == "__main__":
    # 示例1：使用当前TokenTracker生成可视化
    with TokenTracker(session_id="patent_gen_001") as tracker:
        # 记录使用
        tracker.add_tokens(
            operation=OperationType.PLANNING,
            agent_name="PlannerAgent",
            input_tokens=1500,
            output_tokens=800,
            model_name="deepseek-chat",
            response_time=2.5,
            success=True
        )

        # 获取统计
        stats = tracker.get_usage()
        print(f"总成本: ${stats['total_cost']:.4f}")

        # 导出报告
        report_path = tracker.export_usage(filepath='../output/token_records', format_type="xlsx")

        # 生成可视化报告
        visualizer = TokenUsageVisualizer(tracker)
        dashboard_path = visualizer.create_usage_dashboard(output_dir="../output/token_reports")

    # 示例2：从导出文件生成可视化
    export_path = "../output/token_records/token_usage_patent_gen_001_20250609_151220.CSV"
    if os.path.exists(export_path):
        file_visualizer = TokenUsageVisualizer.from_export_file(export_path)
        file_dashboard_path = file_visualizer.create_usage_dashboard(output_dir="../output/exported_token_files_reports")
        print(f"已从导出文件生成报告: {file_dashboard_path}")

    # 示例3：合并多个文件的数据并生成对比报告
    try:
        # 获取utils目录下的所有json导出文件
        json_files = glob.glob("../output/token_records/token_usage_*.json")

        if json_files:
            comparison_visualizer = TokenUsageVisualizer()
            comparison_visualizer.merge_export_files(json_files)
            comparison_path = comparison_visualizer.create_usage_dashboard(output_dir="../output/comparison_token_files_reports")
            print(f"已生成会话对比报告: {comparison_path}")
    except Exception as e:
        print(f"生成对比报告失败: {e}")
