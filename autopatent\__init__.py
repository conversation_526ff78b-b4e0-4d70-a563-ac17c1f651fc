"""
AutoPatent - 智能专利生成系统

基于多智能体协作的专利申请文档自动生成系统，
支持RRAG检索增强审查和智能化工作流管理。
"""

__version__ = "0.1.0"
__author__ = "<PERSON><PERSON><PERSON><PERSON>"
__email__ = "<EMAIL>"
__description__ = "AI-powered patent generation system with multi-agent collaboration"

# 导入核心组件
try:
    from .coordinator.coordinator import AutoPatentCoordinator
    from .coordinator.workflow_manager import WorkflowManager
except ImportError:
    # 如果模块不存在，提供占位符
    AutoPatentCoordinator = None
    WorkflowManager = None

# 导入智能体
try:
    from .agents.base_agent import BaseAgent, AgentStatus, AgentCapability
    from .agents.planner_agent import PlannerAgent
    from .agents.writer_agent import WriterAgent
    from .agents.examiner_agent import ExaminerAgent
except ImportError:
    # 如果模块不存在，提供占位符
    BaseAgent = None
    AgentStatus = None
    AgentCapability = None
    PlannerAgent = None
    WriterAgent = None
    ExaminerAgent = None

# 导入工具类
try:
    from .utils.token_tracker import TokenTracker, OperationType
    from .utils.llm_client import LLMClient, ModelConfig, ModelProvider
except ImportError:
    # 如果模块不存在，提供占位符
    TokenTracker = None
    OperationType = None
    LLMClient = None
    ModelConfig = None
    ModelProvider = None

# 导入数据库
try:
    from .database.patent_db import PatentDB
except ImportError:
    PatentDB = None

# 导出主要类和枚举
__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__description__",

    # 核心组件
    "AutoPatentCoordinator",
    "WorkflowManager",

    # 智能体
    "BaseAgent",
    "PlannerAgent",
    "WriterAgent",
    "ExaminerAgent",

    # 智能体相关枚举
    "AgentStatus",
    "AgentCapability",

    # 工具类
    "TokenTracker",
    "OperationType",
    "LLMClient",
    "ModelConfig",
    "ModelProvider",

    # 数据库
    "PatentDB",
]

# 配置日志
import logging

def setup_logging(level=logging.INFO):
    """设置日志配置"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )

# 默认设置日志
setup_logging()
