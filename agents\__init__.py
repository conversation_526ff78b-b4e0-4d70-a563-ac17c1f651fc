"""
智能体模块 - Multi-Agent System for Patent Generation

包含规划、写作、审查等专门化智能体，
支持协作式专利文档生成和质量控制。
"""

from .base_agent import (
    BaseAgent,
    AgentStatus,
    AgentCapability
)

from .planner_agent import (
    PlannerAgent,
    PlanningStrategy,
    PatentType,
    PriorityLevel,
    SectionPlan,
    PGTree as PlannerPGTree  # 避免与utils中的PGTree冲突
)

from .writer_agent import (
    WriterAgent,
    WriterType,
    WritingStyle,
    QualityLevel,
    WritingTask,
    ContentOptimization
)

from .examiner_agent import (
    ExaminerAgent,
    ExaminationType,
    IssueType,
    ExaminationCriteria,
    ExaminationIssue,
    RRAGResult
)

# 智能体工厂函数
def create_planning_agent(strategy: PlanningStrategy = PlanningStrategy.STANDARD,
                         **kwargs) -> PlannerAgent:
    """
    创建规划智能体

    Args:
        strategy: 规划策略
        **kwargs: 其他配置参数

    Returns:
        配置好的规划智能体
    """
    return PlannerAgent(
        default_strategy=strategy,
        **kwargs
    )

def create_writing_agent(writer_type: WriterType = WriterType.GENERAL,
                        style: WritingStyle = WritingStyle.FORMAL,
                        **kwargs) -> WriterAgent:
    """
    创建写作智能体

    Args:
        writer_type: 写作类型
        style: 写作风格
        **kwargs: 其他配置参数

    Returns:
        配置好的写作智能体
    """
    return WriterAgent(
        writer_type=writer_type,
        writing_style=style,
        **kwargs
    )

def create_examination_agent(enable_rrag: bool = True,
                           **kwargs) -> ExaminerAgent:
    """
    创建审查智能体

    Args:
        enable_rrag: 是否启用RRAG机制
        **kwargs: 其他配置参数

    Returns:
        配置好的审查智能体
    """
    # 需要patent_db参数
    if 'patent_db' not in kwargs:
        raise ValueError("ExaminerAgent需要patent_db参数")

    return ExaminerAgent(
        enable_rrag=enable_rrag,
        **kwargs
    )

def create_agent_team(patent_db,
                     planning_strategy: PlanningStrategy = PlanningStrategy.STANDARD,
                     writer_type: WriterType = WriterType.GENERAL,
                     enable_rrag: bool = True) -> dict:
    """
    创建完整的智能体团队

    Args:
        patent_db: 专利数据库实例
        planning_strategy: 规划策略
        writer_type: 写作类型
        enable_rrag: 是否启用RRAG

    Returns:
        智能体字典 {'planner': PlannerAgent, 'writer': WriterAgent, 'examiner': ExaminerAgent}
    """
    return {
        'planner': create_planning_agent(strategy=planning_strategy),
        'writer': create_writing_agent(writer_type=writer_type),
        'examiner': create_examination_agent(patent_db=patent_db, enable_rrag=enable_rrag)
    }

# 导出的公共接口
__all__ = [
    # 基础智能体
    "BaseAgent",
    "AgentStatus",
    "AgentCapability",

    # 规划智能体
    "PlannerAgent",
    "PlanningStrategy",
    "PatentType",
    "PriorityLevel",
    "SectionPlan",
    "PlannerPGTree",

    # 写作智能体
    "WriterAgent",
    "WriterType",
    "WritingStyle",
    "QualityLevel",
    "WritingTask",
    "ContentOptimization",

    # 审查智能体
    "ExaminerAgent",
    "ExaminationType",
    "IssueType",
    "ExaminationCriteria",
    "ExaminationIssue",
    "RRAGResult",

    # 工厂函数
    "create_planning_agent",
    "create_writing_agent",
    "create_examination_agent",
    "create_agent_team",
]