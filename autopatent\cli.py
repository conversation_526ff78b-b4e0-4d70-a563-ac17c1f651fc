#!/usr/bin/env python3
"""
AutoPatent命令行界面
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import Dict, Any

def setup_logging(level=logging.INFO):
    """设置日志"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查API密钥
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if api_key:
        print("✅ DEEPSEEK_API_KEY已设置")
    else:
        print("⚠️  DEEPSEEK_API_KEY未设置（演示模式仍可运行）")
    
    return True

def run_demo():
    """运行演示模式"""
    print("🎭 启动演示模式...")
    
    # 导入简化的演示
    try:
        from .examples.simple_demo import SimpleCoordinator
        
        # 示例概念
        concept = """
        一种基于机器学习的智能家居能耗管理系统，该系统通过预测用户用电模式并自动控制智能设备来优化能耗。
        
        系统的核心创新包括：
        1. 实时用电需求预测算法，基于历史数据和用户行为模式
        2. 自适应调度算法，能够根据电价波动和用户偏好动态调整设备运行
        3. 多协议集成技术，无缝连接各种智能家居设备
        """
        
        options = {
            'technical_field': '智能家居技术',
            'patent_type': 'invention'
        }
        
        print(f"📝 专利概念: {concept[:100]}...")
        print(f"🔬 技术领域: {options['technical_field']}")
        
        # 创建协调器并生成专利
        coordinator = SimpleCoordinator()
        result = coordinator.generate_patent(concept, options)
        
        if result['success']:
            print("\n✅ 专利生成成功！")
            print(f"📊 质量评分: {result.get('quality_score', 0):.1f}/10")
            
            # 显示简要内容
            patent_content = result.get('patent_content', {})
            for section, content in patent_content.items():
                print(f"\n【{section.upper()}】")
                preview = content[:150] + "..." if len(content) > 150 else content
                print(preview)
        else:
            print(f"\n❌ 专利生成失败: {result.get('error', 'Unknown error')}")
            
    except ImportError as e:
        print(f"❌ 导入演示模块失败: {e}")
        print("请确保所有依赖已正确安装")

def run_interactive():
    """运行交互式模式"""
    print("🎯 启动交互式模式...")
    
    print("\n请输入您的发明概念：")
    concept = input("> ")
    
    if not concept.strip():
        print("❌ 未输入发明概念")
        return
    
    print("\n请输入技术领域（可选，回车使用默认）：")
    field = input("> ") or "通用技术领域"
    
    options = {
        'technical_field': field,
        'patent_type': 'invention'
    }
    
    # 运行生成
    try:
        from .examples.simple_demo import SimpleCoordinator
        
        coordinator = SimpleCoordinator()
        result = coordinator.generate_patent(concept, options)
        
        if result['success']:
            print("\n✅ 专利生成成功！")
            
            # 询问是否保存
            save_choice = input("\n是否保存结果到文件？(y/N): ")
            if save_choice.lower() == 'y':
                import json
                import time
                filename = f"patent_{int(time.time())}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2, default=str)
                print(f"✅ 结果已保存到：{filename}")
        else:
            print(f"\n❌ 专利生成失败: {result.get('error', 'Unknown error')}")
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")

def generate_patent_cli(concept: str, field: str, output_path: str = None):
    """命令行模式生成专利"""
    print(f"\n🚀 正在生成专利...")
    print(f"📝 概念：{concept[:100]}...")
    print(f"🔬 技术领域：{field}")
    
    try:
        from .examples.simple_demo import SimpleCoordinator
        
        coordinator = SimpleCoordinator()
        options = {
            'technical_field': field,
            'patent_type': 'invention'
        }
        
        result = coordinator.generate_patent(concept, options)
        
        if result['success']:
            print("✅ 专利生成成功")
            
            # 保存结果
            if output_path:
                import json
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2, default=str)
                print(f"✅ 结果已保存到：{output_path}")
            else:
                # 显示简要结果
                patent_content = result.get('patent_content', {})
                for section, content in patent_content.items():
                    print(f"\n【{section.upper()}】")
                    print(content[:200] + "...")
        else:
            print(f"❌ 专利生成失败：{result.get('error', 'Unknown error')}")
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AutoPatent - 多智能体专利生成系统')
    parser.add_argument('--concept', '-c', type=str, help='专利概念描述')
    parser.add_argument('--field', '-f', type=str, default='通用技术', help='技术领域')
    parser.add_argument('--output', '-o', type=str, help='输出文件路径')
    parser.add_argument('--demo', action='store_true', help='运行演示模式')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互式模式')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        setup_logging(logging.DEBUG)
    else:
        setup_logging(logging.INFO)
    
    print("🚀 AutoPatent - 多智能体专利生成系统")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    if args.demo:
        run_demo()
    elif args.interactive:
        run_interactive()
    elif args.concept:
        generate_patent_cli(args.concept, args.field, args.output)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
