"""
审查智能体 - 负责专利内容的质量审查和RRAG检索增强审查
"""

import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from enum import Enum
from datetime import datetime
import re
import json
from dataclasses import dataclass, field
import math

from .base_agent import BaseAgent
from ..database.patent_db import PatentDB, SearchResult
from ..utils.pg_tree_handler import PGTreeHandler, NodeStatus
from ..utils.token_tracker import OperationType


class ExaminationType(Enum):
    """审查类型枚举"""
    PRELIMINARY = "preliminary"  # 初步审查
    SUBSTANTIVE = "substantive"  # 实质审查
    NOVELTY = "novelty"  # 新颖性审查
    INVENTIVENESS = "inventiveness"  # 创造性审查
    INDUSTRIAL_APPLICATION = "industrial_application"  # 实用性审查
    RRAG_ENHANCED = "rrag_enhanced"  # RRAG增强审查


class IssueType(Enum):
    """问题类型枚举"""
    CRITICAL = "critical"  # 严重问题
    MAJOR = "major"  # 主要问题
    MINOR = "minor"  # 次要问题
    SUGGESTION = "suggestion"  # 建议
    INFORMATION = "information"  # 信息提示


class ExaminationCriteria(Enum):
    """审查标准枚举"""
    NOVELTY = "novelty"  # 新颖性
    INVENTIVENESS = "inventiveness"  # 创造性
    INDUSTRIAL_APPLICATION = "industrial_application"  # 实用性
    SUFFICIENCY = "sufficiency"  # 充分公开
    CLARITY = "clarity"  # 清楚
    CONCISENESS = "conciseness"  # 简洁
    SUPPORT = "support"  # 说明书支持
    UNITY = "unity"  # 单一性


@dataclass
class ExaminationIssue:
    """审查问题数据类"""
    issue_type: IssueType
    criteria: ExaminationCriteria
    section: str
    description: str
    severity_score: float
    location: str = ""
    suggestion: str = ""
    prior_art_references: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'issue_type': self.issue_type.value,
            'criteria': self.criteria.value,
            'section': self.section,
            'description': self.description,
            'severity_score': self.severity_score,
            'location': self.location,
            'suggestion': self.suggestion,
            'prior_art_references': self.prior_art_references
        }


@dataclass
class RRAGResult:
    """RRAG检索结果数据类"""
    query_concept: str
    retrieved_patents: List[SearchResult]
    novelty_analysis: Dict[str, Any]
    similarity_scores: List[float]
    risk_assessment: str
    recommendations: List[str]


class ExaminerAgent(BaseAgent):
    """审查智能体 - 实现RRAG机制的专利审查"""

    def __init__(self,
                 patent_db: PatentDB,
                 examination_standards: Dict[str, float] = None,
                 enable_rrag: bool = True,
                 **kwargs):
        """
        初始化审查智能体

        Args:
            patent_db: 专利数据库实例
            examination_standards: 审查标准配置
            enable_rrag: 是否启用RRAG机制
            **kwargs: 基类参数
        """
        super().__init__(agent_name="ExaminerAgent", **kwargs)

        self.patent_db = patent_db
        self.enable_rrag = enable_rrag

        # 审查标准配置
        self.examination_standards = examination_standards or {
            'novelty_threshold': 0.7,  # 新颖性阈值
            'inventiveness_threshold': 0.6,  # 创造性阈值
            'similarity_threshold': 0.8,  # 相似度阈值
            'quality_threshold': 7.0,  # 质量阈值
            'min_word_count': 100,  # 最小字数
            'max_similarity_allowed': 0.9  # 允许的最大相似度
        }

        # 审查权重配置
        self.criteria_weights = {
            ExaminationCriteria.NOVELTY: 0.25,
            ExaminationCriteria.INVENTIVENESS: 0.25,
            ExaminationCriteria.INDUSTRIAL_APPLICATION: 0.15,
            ExaminationCriteria.SUFFICIENCY: 0.15,
            ExaminationCriteria.CLARITY: 0.10,
            ExaminationCriteria.CONCISENESS: 0.05,
            ExaminationCriteria.SUPPORT: 0.05
        }

        # 审查模板和指南
        self.examination_templates = self._load_examination_templates()
        self.prior_art_keywords = []

        # 审查统计
        self.examination_stats = {
            "total_examinations": 0,
            "rrag_searches_performed": 0,
            "average_examination_time": 0.0,
            "issues_found": {"critical": 0, "major": 0, "minor": 0},
            "average_quality_score": 0.0,
            "novelty_checks": 0,
            "inventiveness_checks": 0
        }

        # RRAG缓存
        self._rrag_cache = {}
        self._cache_max_size = 100

        self.logger.info(f"ExaminerAgent初始化完成 - RRAG: {'启用' if enable_rrag else '禁用'}")

    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理审查请求的主方法

        Args:
            input_data: 包含pgtree、section等审查信息

        Returns:
            审查结果字典
        """
        start_time = datetime.now()

        try:
            # 解析输入数据
            pgtree = input_data.get('pgtree')
            section = input_data.get('section', 'all')
            examination_type = ExaminationType(input_data.get('examination_type', 'substantive'))
            workflow_id = input_data.get('workflow_id', '')
            iteration = input_data.get('iteration', 1)

            if not pgtree:
                return self._create_error_result("缺少必要的输入参数: pgtree")

            self.logger.info(f"开始审查 - section: {section}, 类型: {examination_type.value}")

            # 执行审查
            if section == 'all':
                examination_result = self._examine_all_sections(pgtree, examination_type)
            else:
                examination_result = self._examine_single_section(pgtree, section, examination_type)

            # 如果启用RRAG，执行检索增强审查
            rrag_result = None
            if self.enable_rrag and examination_type in [ExaminationType.SUBSTANTIVE, ExaminationType.RRAG_ENHANCED]:
                rrag_result = self._perform_rrag_examination(pgtree, section)

                # 将RRAG结果整合到审查结果中
                examination_result = self._integrate_rrag_results(examination_result, rrag_result)

            # 计算总体评分
            overall_score = self._calculate_overall_score(examination_result['issues'])

            # 生成审查建议
            recommendations = self._generate_recommendations(examination_result['issues'], rrag_result)

            # 更新统计信息
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_examination_stats(examination_result, processing_time, overall_score)

            # 记录token使用
            input_tokens, output_tokens = self._estimate_token_usage(input_data, examination_result)
            self.token_tracker.add_tokens(
                operation=OperationType.EXAMINING,
                agent_name=self.agent_name,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                model_name="deepseek-chat",
                response_time=processing_time,
                workflow_id=workflow_id,
                node_id=section,
                success=True,
                complexity_score=len(examination_result['issues'])
            )

            self.logger.info(
                f"审查完成 - 总体评分: {overall_score:.2f}, 发现 {len(examination_result['issues'])} 个问题")

            return {
                'success': True,
                'overall_score': overall_score,
                'examination_type': examination_type.value,
                'section': section,
                'issues': [issue.to_dict() for issue in examination_result['issues']],
                'recommendations': recommendations,
                'rrag_result': rrag_result.__dict__ if rrag_result else None,
                'examination_metadata': {
                    'iteration': iteration,
                    'processing_time': processing_time,
                    'issues_count': len(examination_result['issues']),
                    'rrag_enabled': self.enable_rrag,
                    'prior_art_found': len(rrag_result.retrieved_patents) if rrag_result else 0
                },
                'detailed_analysis': examination_result.get('detailed_analysis', {}),
                'pass_criteria': overall_score >= self.examination_standards['quality_threshold']
            }

        except Exception as e:
            self.logger.error(f"审查处理失败: {e}")
            return self._create_error_result(f"审查异常: {e}")

    def _examine_all_sections(self, pgtree: PGTreeHandler, examination_type: ExaminationType) -> Dict[str, Any]:
        """审查所有section"""
        all_issues = []
        detailed_analysis = {}

        # 获取所有已完成的section
        completed_sections = []
        if hasattr(pgtree, 'nodes'):
            for node_id, node_data in pgtree.nodes.items():
                if (hasattr(node_data, 'status') and
                        node_data.status == NodeStatus.COMPLETED and
                        hasattr(node_data, 'content') and
                        node_data.content):
                    completed_sections.append((node_id, node_data))

        if not completed_sections:
            self.logger.warning("没有找到已完成的section可供审查")
            return {'issues': [], 'detailed_analysis': {}}

        # 逐个审查section
        for section_id, section_data in completed_sections:
            self.logger.debug(f"审查section: {section_id}")

            section_result = self._examine_section_content(
                section_id,
                section_data.content,
                examination_type,
                getattr(section_data, 'section_name', section_id)
            )

            all_issues.extend(section_result['issues'])
            detailed_analysis[section_id] = section_result['analysis']

        # 执行整体一致性检查
        consistency_issues = self._check_overall_consistency(completed_sections)
        all_issues.extend(consistency_issues)

        return {
            'issues': all_issues,
            'detailed_analysis': detailed_analysis,
            'sections_examined': len(completed_sections)
        }

    def _examine_single_section(self,
                                pgtree: PGTreeHandler,
                                section: str,
                                examination_type: ExaminationType) -> Dict[str, Any]:
        """审查单个section"""
        if not hasattr(pgtree, 'nodes') or section not in pgtree.nodes:
            return {
                'issues': [ExaminationIssue(
                    issue_type=IssueType.CRITICAL,
                    criteria=ExaminationCriteria.SUFFICIENCY,
                    section=section,
                    description=f"Section {section} 不存在或未完成",
                    severity_score=10.0
                )],
                'detailed_analysis': {}
            }

        node_data = pgtree.nodes[section]
        if not hasattr(node_data, 'content') or not node_data.content:
            return {
                'issues': [ExaminationIssue(
                    issue_type=IssueType.CRITICAL,
                    criteria=ExaminationCriteria.SUFFICIENCY,
                    section=section,
                    description=f"Section {section} 内容为空",
                    severity_score=10.0
                )],
                'detailed_analysis': {}
            }

        section_result = self._examine_section_content(
            section,
            node_data.content,
            examination_type,
            getattr(node_data, 'section_name', section)
        )

        return {
            'issues': section_result['issues'],
            'detailed_analysis': {section: section_result['analysis']}
        }

    def _examine_section_content(self,
                                 section_id: str,
                                 content: str,
                                 examination_type: ExaminationType,
                                 section_name: str = None) -> Dict[str, Any]:
        """审查具体section内容"""
        issues = []
        analysis = {
            'word_count': len(content.split()),
            'character_count': len(content),
            'paragraph_count': len(content.split('\n\n')),
            'examination_criteria_scores': {}
        }

        section_display_name = section_name or section_id

        # 基础格式检查
        format_issues = self._check_basic_format(section_id, content, section_display_name)
        issues.extend(format_issues)

        # 内容充分性检查
        sufficiency_issues, sufficiency_score = self._check_sufficiency(section_id, content, section_display_name)
        issues.extend(sufficiency_issues)
        analysis['examination_criteria_scores'][ExaminationCriteria.SUFFICIENCY.value] = sufficiency_score

        # 清晰度检查
        clarity_issues, clarity_score = self._check_clarity(section_id, content, section_display_name)
        issues.extend(clarity_issues)
        analysis['examination_criteria_scores'][ExaminationCriteria.CLARITY.value] = clarity_score

        # 简洁性检查
        conciseness_issues, conciseness_score = self._check_conciseness(section_id, content, section_display_name)
        issues.extend(conciseness_issues)
        analysis['examination_criteria_scores'][ExaminationCriteria.CONCISENESS.value] = conciseness_score

        # Section特定检查
        if section_id == 'claims':
            claims_issues = self._check_claims_specific(content)
            issues.extend(claims_issues)
        elif section_id == 'abstract':
            abstract_issues = self._check_abstract_specific(content)
            issues.extend(abstract_issues)
        elif section_id in ['background', 'summary', 'detailed_description']:
            description_issues = self._check_description_specific(section_id, content)
            issues.extend(description_issues)

        # 技术术语一致性检查
        terminology_issues = self._check_terminology_consistency(section_id, content, section_display_name)
        issues.extend(terminology_issues)

        return {
            'issues': issues,
            'analysis': analysis
        }

    def _check_basic_format(self, section_id: str, content: str, section_name: str) -> List[ExaminationIssue]:
        """检查基础格式"""
        issues = []

        # 字数检查
        word_count = len(content.split())
        min_words = self.examination_standards.get('min_word_count', 100)

        if word_count < min_words:
            issues.append(ExaminationIssue(
                issue_type=IssueType.MAJOR,
                criteria=ExaminationCriteria.SUFFICIENCY,
                section=section_id,
                description=f"{section_name}内容过短，当前{word_count}字，建议至少{min_words}字",
                severity_score=7.0,
                suggestion="请补充更多技术细节和说明"
            ))

        # 空行检查
        if '\n\n\n' in content:
            issues.append(ExaminationIssue(
                issue_type=IssueType.MINOR,
                criteria=ExaminationCriteria.CONCISENESS,
                section=section_id,
                description=f"{section_name}存在多余的空行",
                severity_score=2.0,
                suggestion="清理多余的空行，保持格式整洁"
            ))

        # 标点符号检查
        if content.count('。') < word_count / 50:  # 平均每50字至少一个句号
            issues.append(ExaminationIssue(
                issue_type=IssueType.MINOR,
                criteria=ExaminationCriteria.CLARITY,
                section=section_id,
                description=f"{section_name}句子过长，缺少适当的断句",
                severity_score=3.0,
                suggestion="适当增加句号，提高可读性"
            ))

        return issues

    def _check_sufficiency(self, section_id: str, content: str, section_name: str) -> Tuple[
        List[ExaminationIssue], float]:
        """检查内容充分性"""
        issues = []
        score = 8.0  # 基础分

        # section特定的必要元素检查
        required_elements = {
            'abstract': ['技术领域', '技术方案', '有益效果'],
            'background': ['现有技术', '技术问题'],
            'summary': ['发明目的', '技术方案', '有益效果'],
            'detailed_description': ['实施例', '技术细节'],
            'claims': ['独立权利要求', '技术特征']
        }

        if section_id in required_elements:
            missing_elements = []
            for element in required_elements[section_id]:
                # 简化的元素检测
                element_keywords = {
                    '技术领域': ['领域', '涉及', '关于'],
                    '技术方案': ['方案', '方法', '系统', '装置'],
                    '有益效果': ['效果', '优点', '好处', '益处'],
                    '现有技术': ['现有', '已知', '传统', '常规'],
                    '技术问题': ['问题', '缺点', '不足', '局限'],
                    '发明目的': ['目的', '旨在', '解决'],
                    '实施例': ['实施例', '示例', '例如'],
                    '技术细节': ['步骤', '过程', '细节', '具体'],
                    '独立权利要求': ['1.', '权利要求1'],
                    '技术特征': ['特征在于', '包括', '包含']
                }

                if element in element_keywords:
                    keywords = element_keywords[element]
                    if not any(keyword in content for keyword in keywords):
                        missing_elements.append(element)

            if missing_elements:
                issues.append(ExaminationIssue(
                    issue_type=IssueType.MAJOR,
                    criteria=ExaminationCriteria.SUFFICIENCY,
                    section=section_id,
                    description=f"{section_name}缺少必要元素: {', '.join(missing_elements)}",
                    severity_score=6.0,
                    suggestion=f"请补充{', '.join(missing_elements)}的相关内容"
                ))
                score -= len(missing_elements) * 1.5

        # 内容深度检查
        if len(content.split('\n\n')) < 2:  # 段落数太少
            issues.append(ExaminationIssue(
                issue_type=IssueType.MINOR,
                criteria=ExaminationCriteria.SUFFICIENCY,
                section=section_id,
                description=f"{section_name}内容结构简单，缺少层次性",
                severity_score=4.0,
                suggestion="建议增加段落结构，提供更详细的说明"
            ))
            score -= 1.0

        return issues, max(0, min(10, score))

    def _check_clarity(self, section_id: str, content: str, section_name: str) -> Tuple[List[ExaminationIssue], float]:
        """检查清晰度"""
        issues = []
        score = 8.0

        # 句子长度检查
        sentences = re.split(r'[。！？]', content)
        long_sentences = [s for s in sentences if len(s) > 150]

        if long_sentences:
            issues.append(ExaminationIssue(
                issue_type=IssueType.MINOR,
                criteria=ExaminationCriteria.CLARITY,
                section=section_id,
                description=f"{section_name}存在{len(long_sentences)}个过长的句子",
                severity_score=3.0,
                suggestion="将长句分解为多个短句，提高可读性"
            ))
            score -= len(long_sentences) * 0.5

        # 专业术语定义检查
        technical_terms = self._extract_technical_terms(content)
        undefined_terms = []

        for term in technical_terms[:5]:  # 检查前5个术语
            if term not in content.lower().replace(term.lower(), '') and '定义' not in content:
                undefined_terms.append(term)

        if undefined_terms and section_id != 'claims':  # 权利要求书可以不定义术语
            issues.append(ExaminationIssue(
                issue_type=IssueType.SUGGESTION,
                criteria=ExaminationCriteria.CLARITY,
                section=section_id,
                description=f"{section_name}可能包含未定义的技术术语",
                severity_score=2.0,
                suggestion="考虑为关键技术术语提供定义或解释"
            ))
            score -= 0.5

        # 逻辑连贯性检查
        if not self._check_logical_coherence(content):
            issues.append(ExaminationIssue(
                issue_type=IssueType.MINOR,
                criteria=ExaminationCriteria.CLARITY,
                section=section_id,
                description=f"{section_name}逻辑连贯性可以改进",
                severity_score=3.0,
                suggestion="检查段落间的逻辑关系，增加适当的过渡"
            ))
            score -= 1.0

        return issues, max(0, min(10, score))

    def _check_conciseness(self, section_id: str, content: str, section_name: str) -> Tuple[
        List[ExaminationIssue], float]:
        """检查简洁性"""
        issues = []
        score = 8.0

        # 重复内容检查
        paragraphs = content.split('\n\n')
        if len(paragraphs) > 1:
            # 简化的重复检测
            similar_paragraphs = 0
            for i, para1 in enumerate(paragraphs):
                for j, para2 in enumerate(paragraphs[i + 1:], i + 1):
                    similarity = self._calculate_text_similarity(para1, para2)
                    if similarity > 0.7:
                        similar_paragraphs += 1

            if similar_paragraphs > 0:
                issues.append(ExaminationIssue(
                    issue_type=IssueType.MINOR,
                    criteria=ExaminationCriteria.CONCISENESS,
                    section=section_id,
                    description=f"{section_name}存在重复或高度相似的内容",
                    severity_score=3.0,
                    suggestion="删除或合并重复内容，提高简洁性"
                ))
                score -= similar_paragraphs * 0.5

        # 冗余词汇检查
        redundant_phrases = ['显而易见', '众所周知', '毫无疑问', '不言而喻']
        found_redundant = [phrase for phrase in redundant_phrases if phrase in content]

        if found_redundant:
            issues.append(ExaminationIssue(
                issue_type=IssueType.SUGGESTION,
                criteria=ExaminationCriteria.CONCISENESS,
                section=section_id,
                description=f"{section_name}包含冗余表达: {', '.join(found_redundant)}",
                severity_score=1.0,
                suggestion="删除冗余表达，使表述更加直接"
            ))
            score -= len(found_redundant) * 0.2

        return issues, max(0, min(10, score))

    def _check_claims_specific(self, content: str) -> List[ExaminationIssue]:
        """权利要求书特定检查"""
        issues = []

        # 权利要求编号检查
        claim_numbers = re.findall(r'^(\d+)\.', content, re.MULTILINE)

        if not claim_numbers:
            issues.append(ExaminationIssue(
                issue_type=IssueType.CRITICAL,
                criteria=ExaminationCriteria.CLARITY,
                section='claims',
                description="权利要求书缺少正确的编号格式",
                severity_score=9.0,
                suggestion="请按照规范格式编号权利要求（1. 2. 3. ...）"
            ))
        else:
            # 检查编号连续性
            numbers = [int(num) for num in claim_numbers]
            expected = list(range(1, len(numbers) + 1))

            if numbers != expected:
                issues.append(ExaminationIssue(
                    issue_type=IssueType.MAJOR,
                    criteria=ExaminationCriteria.CLARITY,
                    section='claims',
                    description="权利要求编号不连续或不规范",
                    severity_score=6.0,
                    suggestion="确保权利要求编号从1开始连续编号"
                ))

        # 独立权利要求检查
        if not re.search(r'1\..*特征在于', content):
            issues.append(ExaminationIssue(
                issue_type=IssueType.MAJOR,
                criteria=ExaminationCriteria.CLARITY,
                section='claims',
                description="第1项权利要求格式不规范",
                severity_score=7.0,
                suggestion="独立权利要求应包含'其特征在于'的表述"
            ))

        # 从属权利要求检查
        dependent_claims = re.findall(r'根据权利要求\d+', content)
        if len(claim_numbers) > 1 and not dependent_claims:
            issues.append(ExaminationIssue(
                issue_type=IssueType.MINOR,
                criteria=ExaminationCriteria.SUPPORT,
                section='claims',
                description="建议增加从属权利要求",
                severity_score=3.0,
                suggestion="添加从属权利要求以更好地保护发明的不同方面"
            ))

        return issues

    def _check_abstract_specific(self, content: str) -> List[ExaminationIssue]:
        """摘要特定检查"""
        issues = []

        word_count = len(content.split())

        # 摘要长度检查
        if word_count > 400:
            issues.append(ExaminationIssue(
                issue_type=IssueType.MAJOR,
                criteria=ExaminationCriteria.CONCISENESS,
                section='abstract',
                description=f"摘要过长（{word_count}字），建议控制在400字以内",
                severity_score=5.0,
                suggestion="精简摘要内容，突出核心技术方案"
            ))
        elif word_count < 150:
            issues.append(ExaminationIssue(
                issue_type=IssueType.MINOR,
                criteria=ExaminationCriteria.SUFFICIENCY,
                section='abstract',
                description=f"摘要过短（{word_count}字），建议补充到150字以上",
                severity_score=4.0,
                suggestion="补充技术方案的关键信息"
            ))

        # 摘要结构检查
        if '有益效果' not in content and '技术效果' not in content and '优点' not in content:
            issues.append(ExaminationIssue(
                issue_type=IssueType.MINOR,
                criteria=ExaminationCriteria.SUFFICIENCY,
                section='abstract',
                description="摘要应包含有益效果的描述",
                severity_score=3.0,
                suggestion="添加发明的有益效果或技术优势"
            ))

        return issues

    def _check_description_specific(self, section_id: str, content: str) -> List[ExaminationIssue]:
        """说明书section特定检查"""
        issues = []

        if section_id == 'background':
            # 背景技术检查
            if '现有技术' not in content and '背景技术' not in content:
                issues.append(ExaminationIssue(
                    issue_type=IssueType.MAJOR,
                    criteria=ExaminationCriteria.SUFFICIENCY,
                    section=section_id,
                    description="背景技术部分应明确描述现有技术状况",
                    severity_score=6.0,
                    suggestion="补充相关现有技术的描述"
                ))

            # 检查是否指出了技术问题
            problem_indicators = ['问题', '不足', '缺点', '局限', '困难']
            if not any(indicator in content for indicator in problem_indicators):
                issues.append(ExaminationIssue(
                    issue_type=IssueType.MINOR,
                    criteria=ExaminationCriteria.SUFFICIENCY,
                    section=section_id,
                    description="建议明确指出现有技术存在的问题",
                    severity_score=3.0,
                    suggestion="描述现有技术的不足之处，为发明的必要性提供依据"
                ))

        elif section_id == 'detailed_description':
            # 具体实施方式检查
            if '实施例' not in content and '示例' not in content:
                issues.append(ExaminationIssue(
                    issue_type=IssueType.MAJOR,
                    criteria=ExaminationCriteria.SUFFICIENCY,
                    section=section_id,
                    description="具体实施方式应包含具体的实施例",
                    severity_score=7.0,
                    suggestion="提供至少一个详细的实施例"
                ))

            # 检查技术细节充分性
            technical_detail_indicators = ['步骤', '过程', '方法', '参数', '条件']
            detail_count = sum(1 for indicator in technical_detail_indicators if indicator in content)

            if detail_count < 2:
                issues.append(ExaminationIssue(
                    issue_type=IssueType.MINOR,
                    criteria=ExaminationCriteria.SUFFICIENCY,
                    section=section_id,
                    description="技术细节描述可以更加充分",
                    severity_score=4.0,
                    suggestion="增加实施的具体步骤、参数或条件"
                ))

        return issues

    def _check_terminology_consistency(self, section_id: str, content: str, section_name: str) -> List[
        ExaminationIssue]:
        """检查术语一致性"""
        issues = []

        # 提取技术术语
        technical_terms = self._extract_technical_terms(content)

        # 检查术语变体（简化版本）
        term_variants = {}
        for term in technical_terms:
            # 简化的变体检测
            base_term = term.replace('系统', '').replace('装置', '').replace('方法', '')
            if len(base_term) > 2:
                if base_term not in term_variants:
                    term_variants[base_term] = []
                term_variants[base_term].append(term)

        # 发现可能的不一致术语
        inconsistent_terms = [variants for variants in term_variants.values() if len(variants) > 1]

        if inconsistent_terms:
            issues.append(ExaminationIssue(
                issue_type=IssueType.SUGGESTION,
                criteria=ExaminationCriteria.CLARITY,
                section=section_id,
                description=f"{section_name}中可能存在术语使用不一致",
                severity_score=2.0,
                suggestion="检查并统一技术术语的表述"
            ))

        return issues

    def _check_overall_consistency(self, completed_sections: List[Tuple[str, Any]]) -> List[ExaminationIssue]:
        """检查整体一致性"""
        issues = []

        if len(completed_sections) < 2:
            return issues

        # 收集所有section的关键术语
        all_terms = {}
        for section_id, section_data in completed_sections:
            terms = self._extract_technical_terms(section_data.content)
            all_terms[section_id] = set(terms)

        # 检查核心术语在不同section中的一致性
        if 'abstract' in all_terms and 'summary' in all_terms:
            abstract_terms = all_terms['abstract']
            summary_terms = all_terms['summary']

            # 计算术语重叠度
            overlap = len(abstract_terms.intersection(summary_terms))
            total_unique = len(abstract_terms.union(summary_terms))

            if total_unique > 0 and overlap / total_unique < 0.3:
                issues.append(ExaminationIssue(
                    issue_type=IssueType.MINOR,
                    criteria=ExaminationCriteria.CLARITY,
                    section='overall',
                    description="摘要与发明内容的术语一致性可以改进",
                    severity_score=3.0,
                    suggestion="确保核心技术术语在不同部分保持一致"
                ))

        return issues

    def _perform_rrag_examination(self, pgtree: PGTreeHandler, section: str) -> Optional[RRAGResult]:
        """执行RRAG检索增强审查"""
        try:
            self.logger.info("开始RRAG检索增强审查")

            # 提取要审查的概念
            examination_concept = self._extract_examination_concept(pgtree, section)

            if not examination_concept:
                self.logger.warning("无法提取有效的审查概念")
                return None

            # 检查缓存
            cache_key = self._generate_rrag_cache_key(examination_concept)
            if cache_key in self._rrag_cache:
                self.logger.debug("使用RRAG缓存结果")
                return self._rrag_cache[cache_key]

            # 执行相似专利检索
            self.logger.debug(f"检索相似专利，概念: {examination_concept[:100]}...")
            similar_patents = self.patent_db.search_similar_patents(
                concept=examination_concept,
                limit=10,
                similarity_threshold=0.3
            )

            if not similar_patents:
                self.logger.info("未找到相似的现有专利")
                return RRAGResult(
                    query_concept=examination_concept,
                    retrieved_patents=[],
                    novelty_analysis={'novelty_score': 10.0, 'analysis': '未找到相似现有技术'},
                    similarity_scores=[],
                    risk_assessment='低风险',
                    recommendations=['当前技术方案具有较好的新颖性']
                )

            # 分析新颖性和创造性
            novelty_analysis = self._analyze_novelty(examination_concept, similar_patents)

            # 计算相似度分数
            similarity_scores = [patent.similarity_score for patent in similar_patents if patent.similarity_score]

            # 风险评估
            risk_assessment = self._assess_patent_risk(similarity_scores, novelty_analysis)

            # 生成RRAG建议
            recommendations = self._generate_rrag_recommendations(
                novelty_analysis,
                similar_patents,
                risk_assessment
            )

            rrag_result = RRAGResult(
                query_concept=examination_concept,
                retrieved_patents=similar_patents,
                novelty_analysis=novelty_analysis,
                similarity_scores=similarity_scores,
                risk_assessment=risk_assessment,
                recommendations=recommendations
            )

            # 缓存结果
            self._cache_rrag_result(cache_key, rrag_result)

            # 更新统计
            self.examination_stats['rrag_searches_performed'] += 1

            self.logger.info(f"RRAG审查完成，找到 {len(similar_patents)} 个相似专利")
            return rrag_result

        except Exception as e:
            self.logger.error(f"RRAG审查失败: {e}")
            return None

    def _extract_examination_concept(self, pgtree: PGTreeHandler, section: str) -> str:
        """提取用于审查的核心概念"""
        concept_parts = []

        if hasattr(pgtree, 'nodes'):
            # 优先级顺序：summary > abstract > detailed_description
            priority_sections = ['summary', 'abstract', 'detailed_description']

            for priority_section in priority_sections:
                if priority_section in pgtree.nodes:
                    node_data = pgtree.nodes[priority_section]
                    if (hasattr(node_data, 'content') and
                            node_data.content and
                            hasattr(node_data, 'status') and
                            node_data.status == NodeStatus.COMPLETED):

                        # 提取核心内容（前500字符）
                        content = node_data.content[:500]
                        concept_parts.append(content)

                        if len(concept_parts) >= 2:  # 最多取两个section
                            break

        if not concept_parts:
            # 如果没有找到优先section，使用当前审查的section
            if section != 'all' and hasattr(pgtree, 'nodes') and section in pgtree.nodes:
                node_data = pgtree.nodes[section]
                if hasattr(node_data, 'content') and node_data.content:
                    concept_parts.append(node_data.content[:500])

        return ' '.join(concept_parts)

    def _analyze_novelty(self, examination_concept: str, similar_patents: List[SearchResult]) -> Dict[str, Any]:
        """分析新颖性"""
        if not similar_patents:
            return {
                'novelty_score': 10.0,
                'analysis': '未找到相似现有技术，具有良好的新颖性',
                'risk_level': 'low',
                'conflicting_patents': []
            }

        # 计算最高相似度
        max_similarity = max(patent.similarity_score for patent in similar_patents if patent.similarity_score)

        # 新颖性评分（相似度越高，新颖性越低）
        novelty_score = max(0, 10 - (max_similarity * 10))

        # 识别潜在冲突专利
        conflicting_patents = [
            patent for patent in similar_patents
            if patent.similarity_score and patent.similarity_score > 0.7
        ]

        # 分析结果
        if novelty_score >= 8.0:
            analysis = '技术方案具有较好的新颖性'
            risk_level = 'low'
        elif novelty_score >= 6.0:
            analysis = '技术方案具有一定新颖性，但需要注意与现有技术的区别'
            risk_level = 'medium'
        else:
            analysis = '技术方案与现有技术相似度较高，新颖性存在风险'
            risk_level = 'high'

        return {
            'novelty_score': round(novelty_score, 2),
            'analysis': analysis,
            'risk_level': risk_level,
            'max_similarity': round(max_similarity, 3) if max_similarity else 0,
            'conflicting_patents': [patent.patent_id for patent in conflicting_patents],
            'similar_patent_count': len(similar_patents)
        }

    def _assess_patent_risk(self, similarity_scores: List[float], novelty_analysis: Dict[str, Any]) -> str:
        """评估专利风险"""
        if not similarity_scores:
            return '低风险'

        max_similarity = max(similarity_scores)
        avg_similarity = sum(similarity_scores) / len(similarity_scores)
        novelty_score = novelty_analysis.get('novelty_score', 5.0)

        # 综合风险评估
        if max_similarity > 0.9 or novelty_score < 4.0:
            return '高风险'
        elif max_similarity > 0.7 or avg_similarity > 0.5 or novelty_score < 6.0:
            return '中等风险'
        else:
            return '低风险'

    def _generate_rrag_recommendations(self,
                                       novelty_analysis: Dict[str, Any],
                                       similar_patents: List[SearchResult],
                                       risk_assessment: str) -> List[str]:
        """生成RRAG建议"""
        recommendations = []

        risk_level = novelty_analysis.get('risk_level', 'medium')
        conflicting_patents = novelty_analysis.get('conflicting_patents', [])

        if risk_level == 'high':
            recommendations.append("建议重点审查技术方案的新颖性，确保与现有技术有明显区别")
            recommendations.append("考虑增加更多技术特征或改进技术方案以提高新颖性")

            if conflicting_patents:
                recommendations.append(f"特别关注与专利 {', '.join(conflicting_patents[:3])} 的技术差异")

        elif risk_level == 'medium':
            recommendations.append("建议在权利要求中突出与现有技术的区别特征")
            recommendations.append("在说明书中详细说明技术改进和优势")

        else:
            recommendations.append("技术方案具有良好的新颖性，建议保持当前技术特征")

        # 基于相似专利数量的建议
        if len(similar_patents) > 5:
            recommendations.append("发现较多相似专利，建议进行更详细的现有技术分析")

        # 基于技术领域的建议
        technical_fields = set(patent.technical_field for patent in similar_patents if patent.technical_field)
        if len(technical_fields) > 1:
            recommendations.append("相似专利涉及多个技术领域，建议明确技术方案的应用范围")

        return recommendations

    def _integrate_rrag_results(self,
                                examination_result: Dict[str, Any],
                                rrag_result: Optional[RRAGResult]) -> Dict[str, Any]:
        """将RRAG结果整合到审查结果中"""
        if not rrag_result:
            return examination_result

        issues = examination_result.get('issues', [])

        # 基于RRAG结果添加新颖性相关问题
        novelty_analysis = rrag_result.novelty_analysis
        novelty_score = novelty_analysis.get('novelty_score', 5.0)
        risk_level = novelty_analysis.get('risk_level', 'medium')

        if novelty_score < 6.0:
            severity_score = 10.0 - novelty_score
            issue_type = IssueType.CRITICAL if novelty_score < 4.0 else IssueType.MAJOR

            novelty_issue = ExaminationIssue(
                issue_type=issue_type,
                criteria=ExaminationCriteria.NOVELTY,
                section='overall',
                description=f"新颖性存在风险，评分: {novelty_score:.2f}",
                severity_score=severity_score,
                suggestion="建议详细分析与现有技术的区别，突出创新点",
                prior_art_references=[patent.patent_id for patent in rrag_result.retrieved_patents[:3]]
            )
            issues.append(novelty_issue)

        # 如果发现高相似度专利，添加创造性问题
        if rrag_result.similarity_scores:
            max_similarity = max(rrag_result.similarity_scores)
            if max_similarity > 0.8:
                inventiveness_issue = ExaminationIssue(
                    issue_type=IssueType.MAJOR,
                    criteria=ExaminationCriteria.INVENTIVENESS,
                    section='overall',
                    description=f"发现高相似度现有技术，相似度: {max_similarity:.3f}",
                    severity_score=8.0,
                    suggestion="需要论证技术方案相对于现有技术的创造性",
                    prior_art_references=[patent.patent_id for patent in rrag_result.retrieved_patents
                                          if patent.similarity_score and patent.similarity_score > 0.8]
                )
                issues.append(inventiveness_issue)

        examination_result['issues'] = issues
        return examination_result

    def _calculate_overall_score(self, issues: List[ExaminationIssue]) -> float:
        """计算总体评分"""
        if not issues:
            return 9.0  # 没有问题的基础高分

        # 按问题严重程度计算扣分
        total_deduction = 0.0

        for issue in issues:
            severity_deduction = issue.severity_score * 0.1  # 将0-10分的严重度转换为扣分

            # 根据问题类型调整权重
            if issue.issue_type == IssueType.CRITICAL:
                severity_deduction *= 1.5
            elif issue.issue_type == IssueType.MAJOR:
                severity_deduction *= 1.2
            elif issue.issue_type == IssueType.MINOR:
                severity_deduction *= 0.8
            elif issue.issue_type == IssueType.SUGGESTION:
                severity_deduction *= 0.5

            total_deduction += severity_deduction

        # 基础分10分，扣除问题分数
        final_score = max(0, 10.0 - total_deduction)

        return round(final_score, 2)

    def _generate_recommendations(self,
                                  issues: List[ExaminationIssue],
                                  rrag_result: Optional[RRAGResult]) -> List[str]:
        """生成审查建议"""
        recommendations = []

        # 基于问题生成建议
        issue_types = {}
        for issue in issues:
            issue_type = issue.issue_type
            if issue_type not in issue_types:
                issue_types[issue_type] = []
            issue_types[issue_type].append(issue)

        # 按优先级生成建议
        if IssueType.CRITICAL in issue_types:
            critical_issues = issue_types[IssueType.CRITICAL]
            recommendations.append(f"发现 {len(critical_issues)} 个严重问题，需要立即处理")

            for issue in critical_issues[:3]:  # 显示前3个严重问题
                if issue.suggestion:
                    recommendations.append(f"• {issue.suggestion}")

        if IssueType.MAJOR in issue_types:
            major_issues = issue_types[IssueType.MAJOR]
            recommendations.append(f"发现 {len(major_issues)} 个主要问题，建议优先解决")

        # 添加RRAG建议
        if rrag_result and rrag_result.recommendations:
            recommendations.append("基于现有技术检索的建议：")
            for rrag_rec in rrag_result.recommendations[:3]:
                recommendations.append(f"• {rrag_rec}")

        # 总体改进建议
        if len(issues) > 10:
            recommendations.append("发现较多问题，建议系统性地审查和修订专利申请")
        elif len(issues) <= 3:
            recommendations.append("整体质量良好，只需要进行少量修改")

        return recommendations

    def _extract_technical_terms(self, content: str) -> List[str]:
        """提取技术术语"""
        # 简化的技术术语提取
        # 寻找可能的技术术语：长度大于2的连续中文词汇
        terms = re.findall(r'[\u4e00-\u9fff]{3,}', content)

        # 过滤常用词
        common_words = {'具体实施', '实施方式', '权利要求', '技术方案', '有益效果', '发明内容'}
        technical_terms = [term for term in terms if term not in common_words]

        # 去重并保持顺序
        seen = set()
        unique_terms = []
        for term in technical_terms:
            if term not in seen:
                seen.add(term)
                unique_terms.append(term)

        return unique_terms[:20]  # 返回前20个术语

    def _check_logical_coherence(self, content: str) -> bool:
        """检查逻辑连贯性（简化版本）"""
        paragraphs = content.split('\n\n')

        if len(paragraphs) <= 1:
            return True

        # 简单的连贯性指标：检查是否有逻辑连接词
        coherence_indicators = ['因此', '所以', '由于', '然而', '但是', '此外', '另外', '首先', '其次', '最后']

        coherence_count = sum(1 for indicator in coherence_indicators if indicator in content)

        # 如果连接词数量与段落数量比例合理，认为逻辑连贯
        return coherence_count >= len(paragraphs) * 0.3

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简化版本）"""
        if not text1 or not text2:
            return 0.0

        # 分词
        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 or not words2:
            return 0.0

        # 计算Jaccard相似度
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        return intersection / union if union > 0 else 0.0

    def _generate_rrag_cache_key(self, concept: str) -> str:
        """生成RRAG缓存key"""
        import hashlib
        return hashlib.md5(concept.encode('utf-8')).hexdigest()

    def _cache_rrag_result(self, cache_key: str, result: RRAGResult):
        """缓存RRAG结果"""
        if len(self._rrag_cache) >= self._cache_max_size:
            # 删除最旧的缓存项
            oldest_key = next(iter(self._rrag_cache))
            del self._rrag_cache[oldest_key]

        self._rrag_cache[cache_key] = result

    def _update_examination_stats(self,
                                  examination_result: Dict[str, Any],
                                  processing_time: float,
                                  overall_score: float):
        """更新审查统计信息"""
        self.examination_stats['total_examinations'] += 1

        # 更新平均审查时间
        total_time = (self.examination_stats['average_examination_time'] *
                      (self.examination_stats['total_examinations'] - 1))
        self.examination_stats['average_examination_time'] = (
                (total_time + processing_time) / self.examination_stats['total_examinations']
        )

        # 统计问题类型
        issues = examination_result.get('issues', [])
        for issue in issues:
            issue_type = issue.issue_type.value
            if issue_type in self.examination_stats['issues_found']:
                self.examination_stats['issues_found'][issue_type] += 1

        # 更新平均质量评分
        total_score = (self.examination_stats['average_quality_score'] *
                       (self.examination_stats['total_examinations'] - 1))
        self.examination_stats['average_quality_score'] = (
                (total_score + overall_score) / self.examination_stats['total_examinations']
        )

        # 统计审查类型
        for issue in issues:
            if issue.criteria == ExaminationCriteria.NOVELTY:
                self.examination_stats['novelty_checks'] += 1
            elif issue.criteria == ExaminationCriteria.INVENTIVENESS:
                self.examination_stats['inventiveness_checks'] += 1

    def _estimate_token_usage(self, input_data: Dict[str, Any], result: Dict[str, Any]) -> tuple:
        """估算token使用量"""
        # 简化的token估算
        input_text = str(input_data)
        result_text = str(result)

        input_tokens = int(len(input_text) * 0.8)
        output_tokens = int(len(result_text) * 0.8)

        return input_tokens, output_tokens

    def _load_examination_templates(self) -> Dict[str, str]:
        """加载审查模板"""
        return {
            'novelty_check': "检查技术方案的新颖性，与现有技术进行对比分析",
            'inventiveness_check': "评估技术方案的创造性，分析技术改进的非显而易见性",
            'clarity_check': "检查申请文件的清楚性和完整性",
            'support_check': "验证权利要求是否得到说明书的支持"
        }

    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """创建错误结果"""
        return {
            'success': False,
            'error': error_message,
            'agent_name': self.agent_name,
            'timestamp': datetime.now().isoformat()
        }

    def get_examination_statistics(self) -> Dict[str, Any]:
        """获取审查统计信息"""
        stats = self.examination_stats.copy()

        # 添加配置信息
        stats['configuration'] = {
            'examination_standards': self.examination_standards,
            'criteria_weights': {k.value: v for k, v in self.criteria_weights.items()},
            'rrag_enabled': self.enable_rrag,
            'cache_size': len(self._rrag_cache)
        }

        return stats

    def clear_rrag_cache(self):
        """清空RRAG缓存"""
        self._rrag_cache.clear()
        self.logger.info("RRAG缓存已清空")

    def get_token_usage(self) -> Dict[str, Any]:
        """获取token使用统计"""
        return self.token_tracker.get_usage()

    def reset_statistics(self):
        """重置统计信息"""
        self.examination_stats = {
            "total_examinations": 0,
            "rrag_searches_performed": 0,
            "average_examination_time": 0.0,
            "issues_found": {"critical": 0, "major": 0, "minor": 0},
            "average_quality_score": 0.0,
            "novelty_checks": 0,
            "inventiveness_checks": 0
        }

        self.logger.info(f"{self.agent_name} 统计信息已重置")

if __name__ == "__main__":
    # 创建examiner agent
    with PatentDB("patents.db") as patent_db:
        examiner = ExaminerAgent(
            patent_db=patent_db,
            examination_standards={
                'novelty_threshold': 0.7,
                'quality_threshold': 7.0
            },
            enable_rrag=True
        )

        # 审查请求
        input_data = {
            'pgtree': pgtree_instance,
            'section': 'all',
            'examination_type': 'substantive',
            'workflow_id': 'wf_12345'
        }

        result = examiner.process(input_data)
        if result['success']:
            print(f"总体评分: {result['overall_score']}")
            print(f"发现问题: {len(result['issues'])} 个")
